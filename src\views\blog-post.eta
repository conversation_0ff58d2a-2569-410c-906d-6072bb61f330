<!-- Article Header -->
<article class="py-20 bg-slate-800">
    <div class="container mx-auto px-4">
        <div class="max-w-4xl mx-auto">
            <!-- Back to Blog -->
            <div class="mb-8 fade-in">
                <a href="/blog" class="inline-flex items-center text-blue-400 hover:text-blue-300 transition-colors">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
                    </svg>
                    Back to Blog
                </a>
            </div>
            
            <!-- Article Header -->
            <header class="mb-12 fade-in">
                <div class="flex items-center space-x-4 mb-6">
                    <% if (it.post.featured) { %>
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-600 text-white">
                            <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                            </svg>
                            Featured Post
                        </span>
                    <% } %>
                    
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-600 text-white">
                        <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                        </svg>
                        Published
                    </span>
                </div>
                
                <h1 class="text-4xl md:text-5xl font-bold text-white mb-6 leading-tight">
                    <%= it.post.title %>
                </h1>
                
                <div class="flex flex-wrap items-center gap-6 text-slate-400">
                    <div class="flex items-center space-x-2">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                        </svg>
                        <span>
                            <%= new Date(it.post.published_at || it.post.created_at).toLocaleDateString('en-US', { 
                                year: 'numeric', 
                                month: 'long', 
                                day: 'numeric' 
                            }) %>
                        </span>
                    </div>
                    
                    <div class="flex items-center space-x-2">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                        </svg>
                        <span><%= Math.ceil(it.post.content.split(' ').length / 200) %> min read</span>
                    </div>
                    
                    <div class="flex items-center space-x-2">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                        </svg>
                        <span id="view-count">Loading...</span>
                    </div>
                </div>
                
                <!-- Tags -->
                <% if (it.post.tags && it.post.tags.length > 0) { %>
                    <div class="flex flex-wrap gap-2 mt-6">
                        <% it.post.tags.forEach(tag => { %>
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-slate-700 text-slate-300 border border-slate-600">
                                #<%= tag %>
                            </span>
                        <% }) %>
                    </div>
                <% } %>
            </header>
            
            <!-- Article Content -->
            <div class="blog-content fade-in">
                <%- it.post.content %>
            </div>
            
            <!-- Article Footer -->
            <footer class="mt-16 pt-8 border-t border-slate-700 fade-in">
                <!-- Share Buttons -->
                <div class="flex items-center justify-between flex-wrap gap-4">
                    <div>
                        <h3 class="text-lg font-semibold text-white mb-4">Share this post</h3>
                        <div class="flex space-x-4">
                            <a 
                                href="https://twitter.com/intent/tweet?text=<%= encodeURIComponent(it.post.title) %>&url=<%= encodeURIComponent('http://localhost:3000/blog/' + it.post.slug) %>"
                                target="_blank"
                                rel="noopener noreferrer"
                                class="btn btn-outline flex items-center space-x-2"
                            >
                                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                                </svg>
                                <span>Twitter</span>
                            </a>
                            
                            <a 
                                href="https://www.linkedin.com/sharing/share-offsite/?url=<%= encodeURIComponent('http://localhost:3000/blog/' + it.post.slug) %>"
                                target="_blank"
                                rel="noopener noreferrer"
                                class="btn btn-outline flex items-center space-x-2"
                            >
                                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                                </svg>
                                <span>LinkedIn</span>
                            </a>
                            
                            <button 
                                onclick="copyToClipboard()"
                                class="btn btn-outline flex items-center space-x-2"
                                id="copy-btn"
                            >
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"/>
                                </svg>
                                <span>Copy Link</span>
                            </button>
                        </div>
                    </div>
                    
                    <!-- Reading Progress -->
                    <div class="text-right">
                        <div class="text-sm text-slate-400 mb-2">Reading Progress</div>
                        <div class="w-32 bg-slate-700 rounded-full h-2">
                            <div id="reading-progress" class="bg-blue-500 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                        </div>
                    </div>
                </div>
            </footer>
        </div>
    </div>
</article>

<!-- Related Posts -->
<section class="py-20 bg-slate-900">
    <div class="container mx-auto px-4">
        <div class="max-w-6xl mx-auto">
            <div class="text-center mb-12 fade-in">
                <h2 class="text-3xl font-bold text-white mb-4">Related Posts</h2>
                <p class="text-slate-400">
                    Continue reading with these related articles
                </p>
            </div>
            
            <!-- This would be populated with related posts in a real implementation -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <div class="card fade-in">
                    <div class="h-48 bg-gradient-to-br from-blue-600 to-purple-600 rounded-lg mb-4 flex items-center justify-center">
                        <svg class="w-12 h-12 text-white" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z"/>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-white mb-2">Coming Soon</h3>
                    <p class="text-slate-400 mb-4">More related posts will appear here as the blog grows.</p>
                    <a href="/blog" class="btn btn-outline">Browse All Posts</a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Newsletter CTA -->
<section class="py-20 bg-gradient-to-r from-blue-600 to-purple-600">
    <div class="container mx-auto px-4 text-center">
        <div class="max-w-2xl mx-auto fade-in">
            <h2 class="text-3xl font-bold text-white mb-4">Enjoyed this post?</h2>
            <p class="text-xl text-blue-100 mb-8">
                Subscribe to get notified when I publish new content about web development and technology.
            </p>
            
            <form class="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
                <input 
                    type="email" 
                    placeholder="Enter your email" 
                    class="flex-1 px-4 py-3 rounded-lg text-slate-900 placeholder-slate-500 focus:outline-none focus:ring-2 focus:ring-white"
                    required
                >
                <button type="submit" class="btn bg-white text-blue-600 hover:bg-blue-50 px-6 py-3 whitespace-nowrap">
                    Subscribe
                </button>
            </form>
        </div>
    </div>
</section>

<script>
// Copy to clipboard functionality
function copyToClipboard() {
    const url = window.location.href;
    navigator.clipboard.writeText(url).then(() => {
        const btn = document.getElementById('copy-btn');
        const originalText = btn.innerHTML;
        btn.innerHTML = `
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
            </svg>
            <span>Copied!</span>
        `;
        
        setTimeout(() => {
            btn.innerHTML = originalText;
        }, 2000);
    });
}

// Reading progress indicator
function updateReadingProgress() {
    const article = document.querySelector('.blog-content');
    const progressBar = document.getElementById('reading-progress');
    
    if (!article || !progressBar) return;
    
    const articleTop = article.offsetTop;
    const articleHeight = article.offsetHeight;
    const windowHeight = window.innerHeight;
    const scrollTop = window.pageYOffset;
    
    const progress = Math.min(
        Math.max((scrollTop - articleTop + windowHeight / 2) / articleHeight, 0),
        1
    );
    
    progressBar.style.width = `${progress * 100}%`;
}

// Initialize reading progress
document.addEventListener('DOMContentLoaded', function() {
    updateReadingProgress();
    window.addEventListener('scroll', updateReadingProgress);
    
    // Simulate view count (in a real app, this would be tracked server-side)
    const viewCount = Math.floor(Math.random() * 1000) + 100;
    document.getElementById('view-count').textContent = `${viewCount} views`;
});
</script>
