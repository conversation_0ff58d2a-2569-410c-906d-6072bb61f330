import { getDatabase } from './connection';
import { runMigrations } from './migrate';

const sampleProjects = [
  {
    title: 'Portfolio Website',
    description: 'A modern, responsive portfolio website built with Bun, TypeScript, and Tailwind CSS. Features a clean design with subtle glow effects and monospace typography.',
    technologies: JSON.stringify(['TypeScript', 'Bun', 'SQLite', 'Tailwind CSS', 'Eta', 'Sass']),
    github_url: 'https://github.com/username/portfolio',
    live_url: 'https://portfolio.example.com',
    image_url: '/images/portfolio-preview.jpg',
    featured: true
  },
  {
    title: 'Task Management App',
    description: 'A full-stack task management application with real-time updates, drag-and-drop functionality, and team collaboration features.',
    technologies: JSON.stringify(['React', 'Node.js', 'PostgreSQL', 'Socket.io', 'TypeScript']),
    github_url: 'https://github.com/username/task-manager',
    live_url: 'https://tasks.example.com',
    image_url: '/images/task-manager-preview.jpg',
    featured: true
  },
  {
    title: 'Weather Dashboard',
    description: 'A responsive weather dashboard that displays current conditions and forecasts for multiple cities with beautiful data visualizations.',
    technologies: JSON.stringify(['Vue.js', 'Chart.js', 'OpenWeather API', 'CSS Grid', 'JavaScript']),
    github_url: 'https://github.com/username/weather-dashboard',
    live_url: 'https://weather.example.com',
    image_url: '/images/weather-preview.jpg',
    featured: false
  },
  {
    title: 'E-commerce Platform',
    description: 'A modern e-commerce platform with payment integration, inventory management, and admin dashboard.',
    technologies: JSON.stringify(['Next.js', 'Stripe', 'Prisma', 'PostgreSQL', 'Tailwind CSS']),
    github_url: 'https://github.com/username/ecommerce',
    live_url: null,
    image_url: '/images/ecommerce-preview.jpg',
    featured: false
  },
  {
    title: 'Chat Application',
    description: 'Real-time chat application with rooms, file sharing, and emoji reactions built with modern web technologies.',
    technologies: JSON.stringify(['Socket.io', 'Express', 'MongoDB', 'React', 'Material-UI']),
    github_url: 'https://github.com/username/chat-app',
    live_url: 'https://chat.example.com',
    image_url: '/images/chat-preview.jpg',
    featured: false
  }
];

const sampleBlogPosts = [
  {
    title: 'Building Modern Web Applications with Bun and TypeScript',
    slug: 'building-modern-web-apps-bun-typescript',
    content: `# Building Modern Web Applications with Bun and TypeScript

Bun is revolutionizing the JavaScript runtime landscape with its incredible speed and built-in tooling. In this post, we'll explore how to build modern web applications using Bun and TypeScript.

## Why Bun?

Bun offers several advantages over traditional Node.js:

- **Speed**: Bun is significantly faster than Node.js for most operations
- **Built-in tooling**: No need for separate bundlers, test runners, or package managers
- **TypeScript support**: First-class TypeScript support out of the box
- **Web APIs**: Native support for modern web APIs

## Getting Started

To get started with Bun, simply install it and create a new project:

\`\`\`bash
curl -fsSL https://bun.sh/install | bash
bun init
\`\`\`

## Project Structure

A well-organized project structure is crucial for maintainability:

\`\`\`
src/
  ├── routes/
  ├── database/
  ├── views/
  ├── types/
  └── utils/
\`\`\`

This structure separates concerns and makes the codebase easy to navigate.

## Conclusion

Bun represents the future of JavaScript runtimes, offering unparalleled performance and developer experience. Give it a try in your next project!`,
    excerpt: 'Explore how to build modern web applications using Bun and TypeScript, covering the benefits and getting started guide.',
    published: true,
    featured: true,
    tags: JSON.stringify(['Bun', 'TypeScript', 'Web Development', 'JavaScript'])
  },
  {
    title: 'Mastering Tailwind CSS: Tips and Tricks',
    slug: 'mastering-tailwind-css-tips-tricks',
    content: `# Mastering Tailwind CSS: Tips and Tricks

Tailwind CSS has become one of the most popular utility-first CSS frameworks. Here are some advanced tips and tricks to help you master it.

## Custom Utilities

Create custom utilities for common patterns:

\`\`\`css
@layer utilities {
  .glow-effect {
    @apply transition-all duration-300 hover:shadow-lg hover:shadow-blue-500/50;
  }
}
\`\`\`

## Component Patterns

Use @apply to create reusable component classes:

\`\`\`css
.btn {
  @apply px-4 py-2 rounded-lg font-medium transition-colors;
}

.btn-primary {
  @apply bg-blue-500 text-white hover:bg-blue-600;
}
\`\`\`

## Responsive Design

Tailwind makes responsive design intuitive:

\`\`\`html
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
  <!-- Content -->
</div>
\`\`\`

## Dark Mode

Implement dark mode with ease:

\`\`\`html
<div class="bg-white dark:bg-gray-900 text-gray-900 dark:text-white">
  <!-- Content -->
</div>
\`\`\`

These techniques will help you build more maintainable and scalable CSS with Tailwind.`,
    excerpt: 'Advanced tips and tricks for mastering Tailwind CSS, including custom utilities, component patterns, and responsive design.',
    published: true,
    featured: false,
    tags: JSON.stringify(['Tailwind CSS', 'CSS', 'Web Design', 'Frontend'])
  },
  {
    title: 'The Future of Web Development',
    slug: 'future-of-web-development',
    content: `# The Future of Web Development

Web development is constantly evolving. Let's explore the trends and technologies that will shape the future of web development.

## Emerging Technologies

Several technologies are gaining traction:

- **WebAssembly**: Near-native performance in the browser
- **Edge Computing**: Bringing computation closer to users
- **AI Integration**: Smart applications with built-in AI capabilities
- **Progressive Web Apps**: Native-like experiences on the web

## Development Trends

Key trends to watch:

1. **Component-driven development**
2. **Serverless architecture**
3. **JAMstack approach**
4. **Micro-frontends**

## Tools and Frameworks

The tooling landscape continues to evolve with new frameworks and build tools that prioritize developer experience and performance.

Stay curious and keep learning!`,
    excerpt: 'Exploring the trends and technologies that will shape the future of web development.',
    published: false,
    featured: false,
    tags: JSON.stringify(['Web Development', 'Future Tech', 'Trends'])
  }
];

async function seedProjects(): Promise<void> {
  const db = getDatabase();
  
  console.log('Seeding projects...');
  
  for (const project of sampleProjects) {
    try {
      await db.run(
        `INSERT OR IGNORE INTO projects 
         (title, description, technologies, github_url, live_url, image_url, featured)
         VALUES (?, ?, ?, ?, ?, ?, ?)`,
        [
          project.title,
          project.description,
          project.technologies,
          project.github_url,
          project.live_url,
          project.image_url,
          project.featured
        ]
      );
    } catch (error) {
      console.error(`Failed to seed project: ${project.title}`, error);
    }
  }
  
  console.log('✓ Projects seeded successfully');
}

async function seedBlogPosts(): Promise<void> {
  const db = getDatabase();
  
  console.log('Seeding blog posts...');
  
  for (const post of sampleBlogPosts) {
    try {
      const result = await db.run(
        `INSERT OR IGNORE INTO blog_posts 
         (title, slug, content, excerpt, published, featured, tags, published_at)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          post.title,
          post.slug,
          post.content,
          post.excerpt,
          post.published,
          post.featured,
          post.tags,
          post.published ? new Date().toISOString() : null
        ]
      );
    } catch (error) {
      console.error(`Failed to seed blog post: ${post.title}`, error);
    }
  }
  
  console.log('✓ Blog posts seeded successfully');
}

export async function seedDatabase(): Promise<void> {
  console.log('Starting database seeding...');
  
  // Run migrations first
  await runMigrations();
  
  // Seed data
  await seedProjects();
  await seedBlogPosts();
  
  console.log('Database seeding completed successfully!');
}

// Run seeding if this file is executed directly
if (import.meta.main) {
  try {
    await seedDatabase();
    process.exit(0);
  } catch (error) {
    console.error('Seeding failed:', error);
    process.exit(1);
  }
}
