import { type } from 'arktype';

// Project types
export const ProjectSchema = type({
  id: 'number',
  title: 'string',
  description: 'string',
  technologies: 'string[]',
  github_url: 'string | null',
  live_url: 'string | null',
  image_url: 'string | null',
  featured: 'boolean',
  created_at: 'string',
  updated_at: 'string'
});

export const CreateProjectSchema = type({
  title: 'string',
  description: 'string',
  technologies: 'string[]',
  github_url: 'string | null',
  live_url: 'string | null',
  image_url: 'string | null',
  featured: 'boolean'
});

export const UpdateProjectSchema = type({
  title: 'string | undefined',
  description: 'string | undefined',
  technologies: 'string[] | undefined',
  github_url: 'string | null | undefined',
  live_url: 'string | null | undefined',
  image_url: 'string | null | undefined',
  featured: 'boolean | undefined'
});

// Blog post types
export const BlogPostSchema = type({
  id: 'number',
  title: 'string',
  slug: 'string',
  content: 'string',
  excerpt: 'string | null',
  published: 'boolean',
  featured: 'boolean',
  tags: 'string[]',
  created_at: 'string',
  updated_at: 'string',
  published_at: 'string | null'
});

export const CreateBlogPostSchema = type({
  title: 'string',
  slug: 'string',
  content: 'string',
  excerpt: 'string | null',
  published: 'boolean',
  featured: 'boolean',
  tags: 'string[]'
});

export const UpdateBlogPostSchema = type({
  title: 'string | undefined',
  slug: 'string | undefined',
  content: 'string | undefined',
  excerpt: 'string | null | undefined',
  published: 'boolean | undefined',
  featured: 'boolean | undefined',
  tags: 'string[] | undefined'
});

// Contact form types
export const ContactFormSchema = type({
  name: 'string',
  email: 'string',
  subject: 'string',
  message: 'string'
});

// API Response types
export const ApiResponseSchema = type({
  success: 'boolean',
  message: 'string',
  data: 'unknown | null'
});

export const ErrorResponseSchema = type({
  success: 'boolean',
  message: 'string',
  error: 'string | null'
});

// Pagination types
export const PaginationSchema = type({
  page: 'number',
  limit: 'number',
  total: 'number',
  totalPages: 'number'
});

export const PaginatedResponseSchema = type({
  success: 'boolean',
  message: 'string',
  data: 'unknown[]',
  pagination: PaginationSchema
});

// Configuration types
export const ConfigSchema = type({
  port: 'number',
  host: 'string',
  database: {
    path: 'string'
  },
  views: {
    path: 'string',
    cache: 'boolean'
  },
  static: {
    path: 'string'
  }
});

// Type exports for use in the application
export type Project = typeof ProjectSchema.infer;
export type CreateProject = typeof CreateProjectSchema.infer;
export type UpdateProject = typeof UpdateProjectSchema.infer;

export type BlogPost = typeof BlogPostSchema.infer;
export type CreateBlogPost = typeof CreateBlogPostSchema.infer;
export type UpdateBlogPost = typeof UpdateBlogPostSchema.infer;

export type ContactForm = typeof ContactFormSchema.infer;

export type ApiResponse = typeof ApiResponseSchema.infer;
export type ErrorResponse = typeof ErrorResponseSchema.infer;

export type Pagination = typeof PaginationSchema.infer;
export type PaginatedResponse = typeof PaginatedResponseSchema.infer;

export type Config = typeof ConfigSchema.infer;

// Utility types
export interface ViewData {
  title: string;
  description?: string;
  currentPath: string;
  [key: string]: any;
}

export interface DatabaseConnection {
  query: (sql: string, params?: any[]) => Promise<any>;
  run: (sql: string, params?: any[]) => Promise<any>;
  close: () => Promise<void>;
}

// HTTP Method types
export type HttpMethod = 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';

// Route handler types
export interface RouteContext {
  request: Request;
  params: Record<string, string>;
  query: Record<string, string>;
  body?: any;
}

export type RouteHandler = (context: RouteContext) => Promise<Response> | Response;

export interface Route {
  method: HttpMethod;
  path: string;
  handler: RouteHandler;
}

// Middleware types
export type Middleware = (
  context: RouteContext,
  next: () => Promise<Response> | Response
) => Promise<Response> | Response;
