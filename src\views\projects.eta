<!-- Hero Section -->
<section class="py-20 bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900">
    <div class="container mx-auto px-4">
        <div class="max-w-4xl mx-auto text-center">
            <div class="fade-in">
                <h1 class="text-5xl md:text-6xl font-bold text-white mb-6">
                    My <span class="text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-purple-600">Projects</span>
                </h1>
                <p class="text-xl text-slate-300 leading-relaxed mb-8">
                    A collection of web applications and projects that showcase my skills and passion for development
                </p>
                
                <!-- Filter Buttons -->
                <div class="flex flex-wrap justify-center gap-4">
                    <button 
                        onclick="filterProjects('all')" 
                        class="filter-btn active" 
                        data-filter="all"
                    >
                        All Projects
                    </button>
                    <button 
                        onclick="filterProjects('featured')" 
                        class="filter-btn" 
                        data-filter="featured"
                    >
                        Featured
                    </button>
                    <button 
                        onclick="filterProjects('web')" 
                        class="filter-btn" 
                        data-filter="web"
                    >
                        Web Apps
                    </button>
                    <button 
                        onclick="filterProjects('api')" 
                        class="filter-btn" 
                        data-filter="api"
                    >
                        APIs
                    </button>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Projects Grid -->
<section class="py-20 bg-slate-800">
    <div class="container mx-auto px-4">
        <% if (it.projects && it.projects.length > 0) { %>
            <!-- Projects Grid -->
            <div class="project-grid" id="projects-grid">
                <% it.projects.forEach(project => { %>
                    <div class="project-item" data-category="<%= project.featured ? 'featured' : 'regular' %>">
                        <%~ includeFile('partials/project-card', { project }) %>
                    </div>
                <% }) %>
            </div>
            
            <!-- Pagination -->
            <% if (it.pagination && it.pagination.totalPages > 1) { %>
                <div class="flex justify-center mt-16 fade-in">
                    <nav class="flex items-center space-x-2">
                        <!-- Previous Button -->
                        <% if (it.pagination.page > 1) { %>
                            <a 
                                href="/projects?page=<%= it.pagination.page - 1 %>" 
                                class="btn btn-outline px-4 py-2"
                            >
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
                                </svg>
                                Previous
                            </a>
                        <% } %>
                        
                        <!-- Page Numbers -->
                        <% 
                        const startPage = Math.max(1, it.pagination.page - 2);
                        const endPage = Math.min(it.pagination.totalPages, it.pagination.page + 2);
                        %>
                        
                        <% if (startPage > 1) { %>
                            <a href="/projects?page=1" class="btn btn-ghost px-3 py-2">1</a>
                            <% if (startPage > 2) { %>
                                <span class="px-3 py-2 text-slate-400">...</span>
                            <% } %>
                        <% } %>
                        
                        <% for (let i = startPage; i <= endPage; i++) { %>
                            <a 
                                href="/projects?page=<%= i %>" 
                                class="btn <%= i === it.pagination.page ? 'btn-primary' : 'btn-ghost' %> px-3 py-2"
                            >
                                <%= i %>
                            </a>
                        <% } %>
                        
                        <% if (endPage < it.pagination.totalPages) { %>
                            <% if (endPage < it.pagination.totalPages - 1) { %>
                                <span class="px-3 py-2 text-slate-400">...</span>
                            <% } %>
                            <a href="/projects?page=<%= it.pagination.totalPages %>" class="btn btn-ghost px-3 py-2">
                                <%= it.pagination.totalPages %>
                            </a>
                        <% } %>
                        
                        <!-- Next Button -->
                        <% if (it.pagination.page < it.pagination.totalPages) { %>
                            <a 
                                href="/projects?page=<%= it.pagination.page + 1 %>" 
                                class="btn btn-outline px-4 py-2"
                            >
                                Next
                                <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                                </svg>
                            </a>
                        <% } %>
                    </nav>
                </div>
            <% } %>
            
        <% } else { %>
            <!-- Empty State -->
            <div class="text-center py-20">
                <div class="max-w-md mx-auto">
                    <div class="w-24 h-24 bg-slate-700 rounded-full flex items-center justify-center mx-auto mb-6">
                        <svg class="w-12 h-12 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
                        </svg>
                    </div>
                    <h3 class="text-2xl font-semibold text-white mb-4">No Projects Yet</h3>
                    <p class="text-slate-400 mb-8">
                        I'm currently working on some exciting projects. Check back soon to see my latest work!
                    </p>
                    <a href="/contact" class="btn btn-primary">
                        Get In Touch
                    </a>
                </div>
            </div>
        <% } %>
    </div>
</section>

<!-- Technologies Section -->
<section class="py-20 bg-slate-900">
    <div class="container mx-auto px-4">
        <div class="max-w-4xl mx-auto text-center">
            <div class="fade-in">
                <h2 class="text-4xl font-bold text-white mb-6">Technologies I Use</h2>
                <p class="text-xl text-slate-400 mb-12">
                    The tools and frameworks that power my projects
                </p>
                
                <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-6">
                    <div class="tech-item">
                        <div class="w-16 h-16 bg-blue-600 rounded-lg flex items-center justify-center mx-auto mb-3 glow-effect">
                            <span class="text-white font-bold text-sm">TS</span>
                        </div>
                        <span class="text-slate-300 text-sm">TypeScript</span>
                    </div>
                    
                    <div class="tech-item">
                        <div class="w-16 h-16 bg-cyan-600 rounded-lg flex items-center justify-center mx-auto mb-3 glow-effect">
                            <span class="text-white font-bold text-sm">React</span>
                        </div>
                        <span class="text-slate-300 text-sm">React</span>
                    </div>
                    
                    <div class="tech-item">
                        <div class="w-16 h-16 bg-green-600 rounded-lg flex items-center justify-center mx-auto mb-3 glow-effect">
                            <span class="text-white font-bold text-sm">Node</span>
                        </div>
                        <span class="text-slate-300 text-sm">Node.js</span>
                    </div>
                    
                    <div class="tech-item">
                        <div class="w-16 h-16 bg-orange-600 rounded-lg flex items-center justify-center mx-auto mb-3 glow-effect">
                            <span class="text-white font-bold text-sm">Bun</span>
                        </div>
                        <span class="text-slate-300 text-sm">Bun</span>
                    </div>
                    
                    <div class="tech-item">
                        <div class="w-16 h-16 bg-blue-500 rounded-lg flex items-center justify-center mx-auto mb-3 glow-effect">
                            <span class="text-white font-bold text-sm">TW</span>
                        </div>
                        <span class="text-slate-300 text-sm">Tailwind</span>
                    </div>
                    
                    <div class="tech-item">
                        <div class="w-16 h-16 bg-slate-600 rounded-lg flex items-center justify-center mx-auto mb-3 glow-effect">
                            <span class="text-white font-bold text-sm">SQL</span>
                        </div>
                        <span class="text-slate-300 text-sm">SQLite</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="py-20 bg-gradient-to-r from-blue-600 to-purple-600">
    <div class="container mx-auto px-4 text-center">
        <div class="max-w-3xl mx-auto fade-in">
            <h2 class="text-4xl font-bold text-white mb-6">Have a Project in Mind?</h2>
            <p class="text-xl text-blue-100 mb-8 leading-relaxed">
                I'm always interested in discussing new opportunities and collaborating on exciting projects. 
                Let's turn your ideas into reality.
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="/contact" class="btn bg-white text-blue-600 hover:bg-blue-50 text-lg px-8 py-4">
                    Start a Project
                </a>
                <a href="/blog" class="btn border-white text-white hover:bg-white hover:text-blue-600 text-lg px-8 py-4">
                    Read My Blog
                </a>
            </div>
        </div>
    </div>
</section>

<script>
// Project filtering functionality
function filterProjects(category) {
    const projectItems = document.querySelectorAll('.project-item');
    const filterBtns = document.querySelectorAll('.filter-btn');
    
    // Update active button
    filterBtns.forEach(btn => {
        btn.classList.remove('active');
        if (btn.dataset.filter === category) {
            btn.classList.add('active');
        }
    });
    
    // Filter projects
    projectItems.forEach(item => {
        if (category === 'all') {
            item.style.display = 'block';
            item.classList.add('fade-in');
        } else if (category === 'featured' && item.dataset.category === 'featured') {
            item.style.display = 'block';
            item.classList.add('fade-in');
        } else if (category === 'web' || category === 'api') {
            // You can add more specific filtering logic here based on project technologies
            item.style.display = 'block';
            item.classList.add('fade-in');
        } else {
            item.style.display = 'none';
            item.classList.remove('fade-in');
        }
    });
}

// Initialize filter on page load
document.addEventListener('DOMContentLoaded', function() {
    // Set up filter buttons
    const filterBtns = document.querySelectorAll('.filter-btn');
    filterBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            filterProjects(this.dataset.filter);
        });
    });
});
</script>

<style>
.filter-btn {
    @apply px-4 py-2 rounded-lg border border-slate-600 text-slate-300 hover:border-blue-500 hover:text-blue-400 transition-all duration-300;
}

.filter-btn.active {
    @apply bg-blue-600 border-blue-600 text-white;
}

.tech-item {
    @apply text-center transition-all duration-300 hover:transform hover:scale-110;
}

.project-item {
    @apply transition-all duration-500;
}
</style>
