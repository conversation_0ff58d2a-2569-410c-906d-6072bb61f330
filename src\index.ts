import { handleRequest } from './routes/router';
import { runMigrations } from './database/migrate';
import { getEnvVar, isDevelopment } from './utils';

// Configuration
const config = {
  port: parseInt(getEnvVar('PORT', '3000')),
  host: getEnvVar('HOST', 'localhost'),
  database: {
    path: getEnvVar('DATABASE_PATH', './portfolio.db')
  }
};

// CORS headers for development
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
};

// Request logging middleware
function logRequest(request: Request): void {
  if (isDevelopment()) {
    const timestamp = new Date().toISOString();
    const method = request.method;
    const url = new URL(request.url);
    const path = url.pathname + url.search;
    
    console.log(`[${timestamp}] ${method} ${path}`);
  }
}

// Main server handler
async function server<PERSON>andler(request: Request): Promise<Response> {
  logRequest(request);
  
  // Handle CORS preflight requests
  if (request.method === 'OPTIONS') {
    return new Response(null, {
      status: 200,
      headers: corsHeaders
    });
  }
  
  try {
    const response = await handleRequest(request);
    
    // Add CORS headers in development
    if (isDevelopment()) {
      Object.entries(corsHeaders).forEach(([key, value]) => {
        response.headers.set(key, value);
      });
    }
    
    return response;
  } catch (error) {
    console.error('Server error:', error);
    
    return new Response('Internal Server Error', {
      status: 500,
      headers: {
        'Content-Type': 'text/plain',
        ...(isDevelopment() ? corsHeaders : {})
      }
    });
  }
}

// Initialize database and start server
async function startServer(): Promise<void> {
  try {
    console.log('🚀 Starting Portfolio Server...');
    
    // Run database migrations
    console.log('📊 Running database migrations...');
    await runMigrations();
    console.log('✅ Database migrations completed');
    
    // Start the server
    const server = Bun.serve({
      port: config.port,
      hostname: config.host,
      fetch: serverHandler,
      error(error) {
        console.error('Server error:', error);
        return new Response('Internal Server Error', { status: 500 });
      }
    });
    
    console.log(`🌟 Server running at http://${config.host}:${config.port}`);
    console.log(`📝 Environment: ${process.env.NODE_ENV || 'development'}`);
    console.log(`💾 Database: ${config.database.path}`);
    
    if (isDevelopment()) {
      console.log('\n📋 Available routes:');
      console.log('  🏠 Home:     http://localhost:3000/');
      console.log('  👤 About:    http://localhost:3000/about');
      console.log('  💼 Projects: http://localhost:3000/projects');
      console.log('  📝 Blog:     http://localhost:3000/blog');
      console.log('  📧 Contact:  http://localhost:3000/contact');
      console.log('  🔌 API:      http://localhost:3000/api/projects');
      console.log('\n🛠️  Development mode enabled');
    }
    
    // Graceful shutdown
    process.on('SIGINT', () => {
      console.log('\n🛑 Shutting down server...');
      server.stop();
      process.exit(0);
    });
    
    process.on('SIGTERM', () => {
      console.log('\n🛑 Shutting down server...');
      server.stop();
      process.exit(0);
    });
    
  } catch (error) {
    console.error('❌ Failed to start server:', error);
    process.exit(1);
  }
}

// Start the server
startServer();
