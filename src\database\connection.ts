import { Database } from 'bun:sqlite';
import type { DatabaseConnection } from '@/types';

class SQLiteConnection implements DatabaseConnection {
  private db: Database;

  constructor(path: string) {
    this.db = new Database(path);
    // Enable foreign keys
    this.db.exec('PRAGMA foreign_keys = ON');
    // Enable WAL mode for better performance
    this.db.exec('PRAGMA journal_mode = WAL');
  }

  async query(sql: string, params: any[] = []): Promise<any> {
    try {
      const stmt = this.db.prepare(sql);
      const result = stmt.all(...params);
      return result;
    } catch (error) {
      console.error('Database query error:', error);
      throw error;
    }
  }

  async run(sql: string, params: any[] = []): Promise<any> {
    try {
      const stmt = this.db.prepare(sql);
      const result = stmt.run(...params);
      return result;
    } catch (error) {
      console.error('Database run error:', error);
      throw error;
    }
  }

  async close(): Promise<void> {
    this.db.close();
  }

  // Get a single row
  async get(sql: string, params: any[] = []): Promise<any> {
    try {
      const stmt = this.db.prepare(sql);
      const result = stmt.get(...params);
      return result;
    } catch (error) {
      console.error('Database get error:', error);
      throw error;
    }
  }

  // Execute multiple statements in a transaction
  async transaction(queries: Array<{ sql: string; params?: any[] }>): Promise<void> {
    const transaction = this.db.transaction(() => {
      for (const query of queries) {
        const stmt = this.db.prepare(query.sql);
        stmt.run(...(query.params || []));
      }
    });

    try {
      transaction();
    } catch (error) {
      console.error('Transaction error:', error);
      throw error;
    }
  }

  // Get the underlying database instance for advanced operations
  getDatabase(): Database {
    return this.db;
  }
}

// Singleton database connection
let dbConnection: SQLiteConnection | null = null;

export function getDatabase(): SQLiteConnection {
  if (!dbConnection) {
    const dbPath = process.env.DATABASE_PATH || './portfolio.db';
    dbConnection = new SQLiteConnection(dbPath);
  }
  return dbConnection;
}

export async function closeDatabase(): Promise<void> {
  if (dbConnection) {
    await dbConnection.close();
    dbConnection = null;
  }
}

// Export the connection class for testing
export { SQLiteConnection };
