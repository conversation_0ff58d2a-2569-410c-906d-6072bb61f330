import { getDatabase } from './connection';

const migrations = [
  {
    version: 1,
    name: 'create_projects_table',
    sql: `
      CREATE TABLE IF NOT EXISTS projects (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        title TEXT NOT NULL,
        description TEXT NOT NULL,
        technologies TEXT NOT NULL, -- <PERSON>SON array as string
        github_url TEXT,
        live_url TEXT,
        image_url TEXT,
        featured BOOLEAN DEFAULT FALSE,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      );

      CREATE INDEX IF NOT EXISTS idx_projects_featured ON projects(featured);
      CREATE INDEX IF NOT EXISTS idx_projects_created_at ON projects(created_at);
    `
  },
  {
    version: 2,
    name: 'create_blog_posts_table',
    sql: `
      CREATE TABLE IF NOT EXISTS blog_posts (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        title TEXT NOT NULL,
        slug TEXT UNIQUE NOT NULL,
        content TEXT NOT NULL,
        excerpt TEXT,
        published BOOLEAN DEFAULT FALSE,
        featured BOOLEAN DEFAULT FALSE,
        tags TEXT NOT NULL DEFAULT '[]', -- JSON array as string
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        published_at DATETIME
      );

      CREATE INDEX IF NOT EXISTS idx_blog_posts_slug ON blog_posts(slug);
      CREATE INDEX IF NOT EXISTS idx_blog_posts_published ON blog_posts(published);
      CREATE INDEX IF NOT EXISTS idx_blog_posts_featured ON blog_posts(featured);
      CREATE INDEX IF NOT EXISTS idx_blog_posts_published_at ON blog_posts(published_at);
    `
  },
  {
    version: 3,
    name: 'create_contact_submissions_table',
    sql: `
      CREATE TABLE IF NOT EXISTS contact_submissions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        email TEXT NOT NULL,
        subject TEXT NOT NULL,
        message TEXT NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        read BOOLEAN DEFAULT FALSE
      );

      CREATE INDEX IF NOT EXISTS idx_contact_submissions_created_at ON contact_submissions(created_at);
      CREATE INDEX IF NOT EXISTS idx_contact_submissions_read ON contact_submissions(read);
    `
  },
  {
    version: 4,
    name: 'create_schema_version_table',
    sql: `
      CREATE TABLE IF NOT EXISTS schema_version (
        version INTEGER PRIMARY KEY,
        name TEXT NOT NULL,
        applied_at DATETIME DEFAULT CURRENT_TIMESTAMP
      );
    `
  },
  {
    version: 5,
    name: 'add_triggers_for_updated_at',
    sql: `
      -- Trigger for projects table
      CREATE TRIGGER IF NOT EXISTS update_projects_updated_at
        AFTER UPDATE ON projects
        FOR EACH ROW
      BEGIN
        UPDATE projects SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
      END;

      -- Trigger for blog_posts table
      CREATE TRIGGER IF NOT EXISTS update_blog_posts_updated_at
        AFTER UPDATE ON blog_posts
        FOR EACH ROW
      BEGIN
        UPDATE blog_posts SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
      END;

      -- Trigger for setting published_at when published becomes true
      CREATE TRIGGER IF NOT EXISTS set_blog_posts_published_at
        AFTER UPDATE ON blog_posts
        FOR EACH ROW
        WHEN NEW.published = 1 AND OLD.published = 0
      BEGIN
        UPDATE blog_posts SET published_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
      END;
    `
  }
];

async function getCurrentVersion(): Promise<number> {
  const db = getDatabase();
  
  try {
    const result = await db.get('SELECT MAX(version) as version FROM schema_version');
    return result?.version || 0;
  } catch (error) {
    // Table doesn't exist yet
    return 0;
  }
}

async function applyMigration(migration: typeof migrations[0]): Promise<void> {
  const db = getDatabase();
  
  console.log(`Applying migration ${migration.version}: ${migration.name}`);
  
  try {
    // Execute the migration SQL
    await db.run(migration.sql);
    
    // Record the migration
    await db.run(
      'INSERT OR REPLACE INTO schema_version (version, name) VALUES (?, ?)',
      [migration.version, migration.name]
    );
    
    console.log(`✓ Migration ${migration.version} applied successfully`);
  } catch (error) {
    console.error(`✗ Failed to apply migration ${migration.version}:`, error);
    throw error;
  }
}

export async function runMigrations(): Promise<void> {
  console.log('Starting database migrations...');
  
  const currentVersion = await getCurrentVersion();
  console.log(`Current database version: ${currentVersion}`);
  
  const pendingMigrations = migrations.filter(m => m.version > currentVersion);
  
  if (pendingMigrations.length === 0) {
    console.log('No pending migrations');
    return;
  }
  
  console.log(`Found ${pendingMigrations.length} pending migrations`);
  
  for (const migration of pendingMigrations) {
    await applyMigration(migration);
  }
  
  console.log('All migrations completed successfully!');
}

// Run migrations if this file is executed directly
if (import.meta.main) {
  try {
    await runMigrations();
    process.exit(0);
  } catch (error) {
    console.error('Migration failed:', error);
    process.exit(1);
  }
}
