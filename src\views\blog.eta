<!-- Hero Section -->
<section class="py-20 bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900">
    <div class="container mx-auto px-4">
        <div class="max-w-4xl mx-auto text-center">
            <div class="fade-in">
                <h1 class="text-5xl md:text-6xl font-bold text-white mb-6">
                    My <span class="text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-purple-600">Blog</span>
                </h1>
                <p class="text-xl text-slate-300 leading-relaxed mb-8">
                    Thoughts, tutorials, and insights about web development, technology trends, and programming best practices
                </p>
                
                <!-- Search Bar -->
                <div class="max-w-md mx-auto">
                    <div class="relative">
                        <input 
                            type="text" 
                            placeholder="Search posts..." 
                            class="w-full px-4 py-3 pl-12 bg-slate-800 border border-slate-600 rounded-lg text-white placeholder-slate-400 focus:outline-none focus:border-blue-500 focus:ring-1 focus:ring-blue-500"
                            id="search-input"
                        >
                        <svg class="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                        </svg>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Blog Posts -->
<section class="py-20 bg-slate-800">
    <div class="container mx-auto px-4">
        <% if (it.posts && it.posts.length > 0) { %>
            <!-- Featured Post -->
            <% const featuredPost = it.posts.find(post => post.featured); %>
            <% if (featuredPost) { %>
                <div class="mb-16 fade-in">
                    <h2 class="text-2xl font-bold text-white mb-8 text-center">Featured Post</h2>
                    <div class="max-w-4xl mx-auto">
                        <article class="card group hover:scale-105 transition-all duration-300">
                            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
                                <div>
                                    <div class="flex items-center space-x-2 mb-4">
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-600 text-white">
                                            <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                                            </svg>
                                            Featured
                                        </span>
                                        <span class="text-slate-400 text-sm">
                                            <%= new Date(featuredPost.published_at || featuredPost.created_at).toLocaleDateString('en-US', { 
                                                year: 'numeric', 
                                                month: 'long', 
                                                day: 'numeric' 
                                            }) %>
                                        </span>
                                    </div>
                                    
                                    <h3 class="text-3xl font-bold text-white mb-4 group-hover:text-blue-400 transition-colors">
                                        <a href="/blog/<%= featuredPost.slug %>">
                                            <%= featuredPost.title %>
                                        </a>
                                    </h3>
                                    
                                    <% if (featuredPost.excerpt) { %>
                                        <p class="text-slate-400 leading-relaxed mb-6">
                                            <%= featuredPost.excerpt %>
                                        </p>
                                    <% } %>
                                    
                                    <div class="flex items-center justify-between">
                                        <a href="/blog/<%= featuredPost.slug %>" class="btn btn-primary">
                                            Read More
                                        </a>
                                        <span class="text-slate-500 text-sm">
                                            <%= Math.ceil(featuredPost.content.split(' ').length / 200) %> min read
                                        </span>
                                    </div>
                                </div>
                                
                                <div class="lg:order-first">
                                    <div class="aspect-video bg-gradient-to-br from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
                                        <svg class="w-16 h-16 text-white" fill="currentColor" viewBox="0 0 24 24">
                                            <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z"/>
                                        </svg>
                                    </div>
                                </div>
                            </div>
                        </article>
                    </div>
                </div>
            <% } %>
            
            <!-- All Posts Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8" id="posts-grid">
                <% it.posts.filter(post => !post.featured).forEach(post => { %>
                    <div class="post-item">
                        <%~ includeFile('partials/blog-preview', { post }) %>
                    </div>
                <% }) %>
            </div>
            
            <!-- Pagination -->
            <% if (it.pagination && it.pagination.totalPages > 1) { %>
                <div class="flex justify-center mt-16 fade-in">
                    <nav class="flex items-center space-x-2">
                        <!-- Previous Button -->
                        <% if (it.pagination.page > 1) { %>
                            <a 
                                href="/blog?page=<%= it.pagination.page - 1 %>" 
                                class="btn btn-outline px-4 py-2"
                            >
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
                                </svg>
                                Previous
                            </a>
                        <% } %>
                        
                        <!-- Page Numbers -->
                        <% 
                        const startPage = Math.max(1, it.pagination.page - 2);
                        const endPage = Math.min(it.pagination.totalPages, it.pagination.page + 2);
                        %>
                        
                        <% if (startPage > 1) { %>
                            <a href="/blog?page=1" class="btn btn-ghost px-3 py-2">1</a>
                            <% if (startPage > 2) { %>
                                <span class="px-3 py-2 text-slate-400">...</span>
                            <% } %>
                        <% } %>
                        
                        <% for (let i = startPage; i <= endPage; i++) { %>
                            <a 
                                href="/blog?page=<%= i %>" 
                                class="btn <%= i === it.pagination.page ? 'btn-primary' : 'btn-ghost' %> px-3 py-2"
                            >
                                <%= i %>
                            </a>
                        <% } %>
                        
                        <% if (endPage < it.pagination.totalPages) { %>
                            <% if (endPage < it.pagination.totalPages - 1) { %>
                                <span class="px-3 py-2 text-slate-400">...</span>
                            <% } %>
                            <a href="/blog?page=<%= it.pagination.totalPages %>" class="btn btn-ghost px-3 py-2">
                                <%= it.pagination.totalPages %>
                            </a>
                        <% } %>
                        
                        <!-- Next Button -->
                        <% if (it.pagination.page < it.pagination.totalPages) { %>
                            <a 
                                href="/blog?page=<%= it.pagination.page + 1 %>" 
                                class="btn btn-outline px-4 py-2"
                            >
                                Next
                                <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                                </svg>
                            </a>
                        <% } %>
                    </nav>
                </div>
            <% } %>
            
        <% } else { %>
            <!-- Empty State -->
            <div class="text-center py-20">
                <div class="max-w-md mx-auto">
                    <div class="w-24 h-24 bg-slate-700 rounded-full flex items-center justify-center mx-auto mb-6">
                        <svg class="w-12 h-12 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                        </svg>
                    </div>
                    <h3 class="text-2xl font-semibold text-white mb-4">No Blog Posts Yet</h3>
                    <p class="text-slate-400 mb-8">
                        I'm working on some great content. Check back soon for tutorials, insights, and tech discussions!
                    </p>
                    <a href="/projects" class="btn btn-primary">
                        View My Projects
                    </a>
                </div>
            </div>
        <% } %>
    </div>
</section>

<!-- Newsletter Section -->
<section class="py-20 bg-slate-900">
    <div class="container mx-auto px-4">
        <div class="max-w-2xl mx-auto text-center">
            <div class="fade-in">
                <h2 class="text-3xl font-bold text-white mb-4">Stay Updated</h2>
                <p class="text-slate-400 mb-8">
                    Get notified when I publish new posts about web development, tutorials, and tech insights.
                </p>
                
                <form class="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
                    <input 
                        type="email" 
                        placeholder="Enter your email" 
                        class="flex-1 px-4 py-3 bg-slate-800 border border-slate-600 rounded-lg text-white placeholder-slate-400 focus:outline-none focus:border-blue-500 focus:ring-1 focus:ring-blue-500"
                        required
                    >
                    <button type="submit" class="btn btn-primary px-6 py-3 whitespace-nowrap">
                        Subscribe
                    </button>
                </form>
                
                <p class="text-slate-500 text-sm mt-4">
                    No spam, unsubscribe at any time.
                </p>
            </div>
        </div>
    </div>
</section>

<script>
// Search functionality
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('search-input');
    const postItems = document.querySelectorAll('.post-item');
    
    if (searchInput && postItems.length > 0) {
        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            
            postItems.forEach(item => {
                const title = item.querySelector('h2').textContent.toLowerCase();
                const excerpt = item.querySelector('p') ? item.querySelector('p').textContent.toLowerCase() : '';
                
                if (title.includes(searchTerm) || excerpt.includes(searchTerm)) {
                    item.style.display = 'block';
                } else {
                    item.style.display = 'none';
                }
            });
        });
    }
});
</script>
