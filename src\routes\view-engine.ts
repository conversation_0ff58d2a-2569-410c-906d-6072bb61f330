import { Eta } from 'eta';
import { join } from 'path';
import type { ViewData } from '@/types';

// Initialize Eta template engine
const eta = new Eta({
  views: join(process.cwd(), 'src/views'),
  cache: process.env.NODE_ENV === 'production',
  debug: process.env.NODE_ENV === 'development'
});

export async function renderView(template: string, data: ViewData): Promise<Response> {
  try {
    // Add common data to all views
    const viewData = {
      ...data,
      currentPath: data.currentPath || '/',
      year: new Date().getFullYear(),
      env: process.env.NODE_ENV || 'development'
    };

    // Render the template with layout
    const html = await eta.render('layout', {
      ...viewData,
      body: await eta.render(template, viewData)
    });

    return new Response(html, {
      headers: {
        'Content-Type': 'text/html; charset=utf-8',
        'Cache-Control': process.env.NODE_ENV === 'production' 
          ? 'public, max-age=3600' 
          : 'no-cache'
      }
    });
  } catch (error) {
    console.error('Template rendering error:', error);
    
    // Return a simple error page
    const errorHtml = `
      <!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Error - Portfolio</title>
        <style>
          body { 
            font-family: monospace; 
            background: #0f172a; 
            color: #f8fafc; 
            display: flex; 
            align-items: center; 
            justify-content: center; 
            min-height: 100vh; 
            margin: 0; 
          }
          .error-container { 
            text-align: center; 
            padding: 2rem; 
            border: 1px solid #334155; 
            border-radius: 0.5rem; 
            background: #1e293b; 
          }
          h1 { color: #ef4444; margin-bottom: 1rem; }
          p { color: #94a3b8; margin-bottom: 1.5rem; }
          a { 
            color: #60a5fa; 
            text-decoration: none; 
            padding: 0.5rem 1rem; 
            border: 1px solid #60a5fa; 
            border-radius: 0.25rem; 
            transition: all 0.3s ease; 
          }
          a:hover { 
            background: #60a5fa; 
            color: white; 
          }
        </style>
      </head>
      <body>
        <div class="error-container">
          <h1>Template Error</h1>
          <p>Sorry, there was an error rendering this page.</p>
          <a href="/">Return Home</a>
        </div>
      </body>
      </html>
    `;

    return new Response(errorHtml, {
      status: 500,
      headers: { 'Content-Type': 'text/html; charset=utf-8' }
    });
  }
}

export { eta };
