{"name": "portfolio", "version": "1.0.0", "description": "Personal portfolio website built with Bun, TypeScript, SQLite, and Eta", "module": "src/index.ts", "type": "module", "private": true, "scripts": {"dev": "bun run --watch src/index.ts", "build": "bun build src/index.ts --outdir ./dist --target bun", "start": "bun run dist/index.js", "db:migrate": "bun run src/database/migrate.ts", "db:seed": "bun run src/database/seed.ts", "build:css": "tailwindcss -i ./src/styles/input.scss -o ./public/css/style.css --watch", "build:css:prod": "tailwindcss -i ./src/styles/input.scss -o ./public/css/style.css --minify"}, "dependencies": {"eta": "^3.4.0", "marked": "^12.0.0", "gray-matter": "^4.0.3", "zod": "^3.22.4", "tailwindcss": "^3.4.0", "sass": "^1.69.0", "autoprefixer": "^10.4.16"}, "devDependencies": {"@types/bun": "latest", "@types/marked": "^5.0.0"}, "peerDependencies": {"typescript": "^5"}}