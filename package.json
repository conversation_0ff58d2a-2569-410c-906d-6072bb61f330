{"name": "portfolio", "version": "1.0.0", "description": "Personal portfolio website built with Bun, TypeScript, SQLite, and Eta", "module": "src/index.ts", "type": "module", "private": true, "scripts": {"dev": "bun run --watch src/index.ts", "build": "bun build src/index.ts --outdir ./dist --target bun", "start": "bun run dist/index.js", "db:migrate": "bun run src/database/migrate.ts", "db:seed": "bun run src/database/seed.ts", "build:css": "tailwindcss -i ./src/styles/input.scss -o ./public/css/style.css --watch", "build:css:prod": "tailwindcss -i ./src/styles/input.scss -o ./public/css/style.css --minify"}, "dependencies": {"@tailwindcss/cli": "^4.0.0", "arktype": "latest", "autoprefixer": "latest", "eta": "latest", "gray-matter": "latest", "marked": "latest", "sass": "latest", "tailwindcss": "latest"}, "devDependencies": {"@types/bun": "latest", "@types/marked": "latest"}, "peerDependencies": {"typescript": "^5"}}