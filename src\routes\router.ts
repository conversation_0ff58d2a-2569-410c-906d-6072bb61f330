import type { Route, RouteContext, RouteHandler } from '@/types';
import {
  homeHandler,
  about<PERSON><PERSON><PERSON>,
  projects<PERSON><PERSON><PERSON>,
  blog<PERSON>andler,
  blog<PERSON>ost<PERSON><PERSON>ler,
  contact<PERSON>andler,
  contactSubmitHandler,
  apiProjectsHandler,
  apiProjectHandler,
  apiBlogPostsHandler,
  apiBlogPostHandler
} from './index';

// Route definitions
const routes: Route[] = [
  // Page routes
  { method: 'GET', path: '/', handler: homeHandler },
  { method: 'GET', path: '/about', handler: aboutHandler },
  { method: 'GET', path: '/projects', handler: projectsHandler },
  { method: 'GET', path: '/blog', handler: blogHandler },
  { method: 'GET', path: '/blog/:slug', handler: blogPostHandler },
  { method: 'GET', path: '/contact', handler: contactHandler },
  { method: 'POST', path: '/contact', handler: contactSubmitHandler },

  // API routes
  { method: 'GET', path: '/api/projects', handler: apiProjectsHand<PERSON> },
  { method: 'POST', path: '/api/projects', handler: apiProjectsHandler },
  { method: 'GET', path: '/api/projects/:id', handler: apiProjectHandler },
  { method: 'PUT', path: '/api/projects/:id', handler: apiProjectHandler },
  { method: 'DELETE', path: '/api/projects/:id', handler: apiProjectHandler },
  { method: 'GET', path: '/api/blog', handler: apiBlogPostsHandler },
  { method: 'POST', path: '/api/blog', handler: apiBlogPostsHandler },
  { method: 'GET', path: '/api/blog/:id', handler: apiBlogPostHandler },
  { method: 'PUT', path: '/api/blog/:id', handler: apiBlogPostHandler },
  { method: 'DELETE', path: '/api/blog/:id', handler: apiBlogPostHandler }
];

// Route matching utilities
function parseRoute(pattern: string): { regex: RegExp; paramNames: string[] } {
  const paramNames: string[] = [];
  const regexPattern = pattern
    .replace(/:[^/]+/g, (match) => {
      paramNames.push(match.slice(1));
      return '([^/]+)';
    })
    .replace(/\//g, '\\/');

  return {
    regex: new RegExp(`^${regexPattern}$`),
    paramNames
  };
}

function matchRoute(method: string, path: string): { route: Route; params: Record<string, string> } | null {
  for (const route of routes) {
    if (route.method !== method) continue;

    const { regex, paramNames } = parseRoute(route.path);
    const match = path.match(regex);

    if (match) {
      const params: Record<string, string> = {};
      paramNames.forEach((name, index) => {
        params[name] = match[index + 1];
      });

      return { route, params };
    }
  }

  return null;
}

function parseQuery(url: string): Record<string, string> {
  const query: Record<string, string> = {};
  const urlObj = new URL(url, 'http://localhost');

  urlObj.searchParams.forEach((value, key) => {
    query[key] = value;
  });

  return query;
}

// Main router function
export async function handleRequest(request: Request): Promise<Response> {
  const url = new URL(request.url);
  const method = request.method;
  const path = url.pathname;

  // Handle static files
  if (path.startsWith('/css/') || path.startsWith('/js/') || path.startsWith('/images/') || path.startsWith('/favicon.ico')) {
    return handleStaticFile(path);
  }

  // Find matching route
  const match = matchRoute(method, path);

  if (!match) {
    return handle404();
  }

  // Create route context
  const context: RouteContext = {
    request,
    params: match.params,
    query: parseQuery(request.url),
    body: undefined
  };

  // Parse body for POST/PUT requests
  if (method === 'POST' || method === 'PUT') {
    const contentType = request.headers.get('content-type') || '';

    if (contentType.includes('application/json')) {
      try {
        context.body = await request.json();
      } catch (error) {
        return new Response('Invalid JSON', { status: 400 });
      }
    } else if (contentType.includes('application/x-www-form-urlencoded')) {
      try {
        context.body = await request.formData();
      } catch (error) {
        return new Response('Invalid form data', { status: 400 });
      }
    }
  }

  try {
    return await match.route.handler(context);
  } catch (error) {
    console.error('Route handler error:', error);
    return handle500();
  }
}

// Static file handler
async function handleStaticFile(path: string): Promise<Response> {
  try {
    const filePath = `./public${path}`;
    const file = Bun.file(filePath);

    if (!(await file.exists())) {
      return new Response('File not found', { status: 404 });
    }

    const mimeTypes: Record<string, string> = {
      '.css': 'text/css',
      '.js': 'application/javascript',
      '.png': 'image/png',
      '.jpg': 'image/jpeg',
      '.jpeg': 'image/jpeg',
      '.gif': 'image/gif',
      '.svg': 'image/svg+xml',
      '.ico': 'image/x-icon',
      '.woff': 'font/woff',
      '.woff2': 'font/woff2',
      '.ttf': 'font/ttf',
      '.eot': 'application/vnd.ms-fontobject'
    };

    const ext = path.substring(path.lastIndexOf('.'));
    const mimeType = mimeTypes[ext] || 'application/octet-stream';

    return new Response(file, {
      headers: {
        'Content-Type': mimeType,
        'Cache-Control': 'public, max-age=31536000' // 1 year cache for static files
      }
    });
  } catch (error) {
    console.error('Static file error:', error);
    return new Response('Internal Server Error', { status: 500 });
  }
}

// Error handlers
function handle404(): Response {
  const html = `
    <!DOCTYPE html>
    <html lang="en" data-theme="dark">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>404 - Page Not Found | Portfolio</title>
      <link rel="stylesheet" href="/css/style.css">
    </head>
    <body class="min-h-screen bg-slate-900 text-slate-100 font-mono flex items-center justify-center">
      <div class="text-center">
        <div class="mb-8">
          <h1 class="text-6xl font-bold text-blue-400 mb-4">404</h1>
          <h2 class="text-2xl font-semibold text-white mb-4">Page Not Found</h2>
          <p class="text-slate-400 mb-8 max-w-md mx-auto">
            The page you're looking for doesn't exist or has been moved.
          </p>
        </div>
        <div class="space-x-4">
          <a href="/" class="btn btn-primary">Go Home</a>
          <a href="/projects" class="btn btn-outline">View Projects</a>
        </div>
      </div>
    </body>
    </html>
  `;

  return new Response(html, {
    status: 404,
    headers: { 'Content-Type': 'text/html; charset=utf-8' }
  });
}

function handle500(): Response {
  const html = `
    <!DOCTYPE html>
    <html lang="en" data-theme="dark">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>500 - Server Error | Portfolio</title>
      <link rel="stylesheet" href="/css/style.css">
    </head>
    <body class="min-h-screen bg-slate-900 text-slate-100 font-mono flex items-center justify-center">
      <div class="text-center">
        <div class="mb-8">
          <h1 class="text-6xl font-bold text-red-400 mb-4">500</h1>
          <h2 class="text-2xl font-semibold text-white mb-4">Server Error</h2>
          <p class="text-slate-400 mb-8 max-w-md mx-auto">
            Something went wrong on our end. Please try again later.
          </p>
        </div>
        <div class="space-x-4">
          <a href="/" class="btn btn-primary">Go Home</a>
          <button onclick="window.location.reload()" class="btn btn-outline">Try Again</button>
        </div>
      </div>
    </body>
    </html>
  `;

  return new Response(html, {
    status: 500,
    headers: { 'Content-Type': 'text/html; charset=utf-8' }
  });
}
