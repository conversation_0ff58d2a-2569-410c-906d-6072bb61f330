import type { Config } from 'tailwindcss';

const config: Config = {
  content: [
    './src/views/**/*.eta',
    './src/views/**/*.html',
    './public/**/*.html',
    './src/**/*.ts',
  ],
  theme: {
    extend: {
      fontFamily: {
        mono: [
          'JetBrains Mono',
          'Fira Code',
          'SF Mono',
          'Monaco',
          'Inconsolata',
          'Roboto Mono',
          'Source Code Pro',
          'Menlo',
          'Consolas',
          'DejaVu Sans Mono',
          'monospace',
        ],
      },
      colors: {
        primary: {
          50: '#f0f9ff',
          100: '#e0f2fe',
          200: '#bae6fd',
          300: '#7dd3fc',
          400: '#38bdf8',
          500: '#0ea5e9',
          600: '#0284c7',
          700: '#0369a1',
          800: '#075985',
          900: '#0c4a6e',
          950: '#082f49',
        },
        glow: {
          blue: '#60a5fa',
          purple: '#a78bfa',
          green: '#34d399',
          pink: '#f472b6',
          yellow: '#fbbf24',
        },
      },
      boxShadow: {
        'glow-sm': '0 0 5px rgba(96, 165, 250, 0.5)',
        'glow': '0 0 10px rgba(96, 165, 250, 0.5)',
        'glow-lg': '0 0 20px rgba(96, 165, 250, 0.5)',
        'glow-purple': '0 0 10px rgba(167, 139, 250, 0.5)',
        'glow-green': '0 0 10px rgba(52, 211, 153, 0.5)',
        'glow-pink': '0 0 10px rgba(244, 114, 182, 0.5)',
        'glow-yellow': '0 0 10px rgba(251, 191, 36, 0.5)',
      },
      animation: {
        'pulse-glow': 'pulse-glow 2s ease-in-out infinite alternate',
        'float': 'float 3s ease-in-out infinite',
      },
      keyframes: {
        'pulse-glow': {
          '0%': { boxShadow: '0 0 5px rgba(96, 165, 250, 0.5)' },
          '100%': { boxShadow: '0 0 20px rgba(96, 165, 250, 0.8)' },
        },
        'float': {
          '0%, 100%': { transform: 'translateY(0px)' },
          '50%': { transform: 'translateY(-10px)' },
        },
      },
    },
  },
  plugins: [],
};

export default config;
