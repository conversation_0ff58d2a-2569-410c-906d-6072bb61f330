<!-- Hero Section -->
<section class="relative min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900">
    <div class="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23334155" fill-opacity="0.1"%3E%3Ccircle cx="30" cy="30" r="1"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-50"></div>
    
    <div class="container mx-auto px-4 text-center relative z-10">
        <div class="max-w-4xl mx-auto">
            <!-- Greeting -->
            <div class="fade-in">
                <h1 class="text-5xl md:text-7xl font-bold text-white mb-6">
                    Hello, I'm 
                    <span class="text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-purple-600 animate-pulse-glow">
                        Developer
                    </span>
                </h1>
                
                <p class="text-xl md:text-2xl text-slate-300 mb-8 leading-relaxed">
                    A passionate full-stack developer crafting modern web applications 
                    with cutting-edge technologies and clean, efficient code.
                </p>
            </div>
            
            <!-- CTA Buttons -->
            <div class="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12 fade-in">
                <a href="/projects" class="btn btn-primary text-lg px-8 py-4">
                    View My Work
                </a>
                <a href="/contact" class="btn btn-outline text-lg px-8 py-4">
                    Get In Touch
                </a>
            </div>
            
            <!-- Tech Stack Preview -->
            <div class="fade-in">
                <p class="text-slate-400 mb-6">Built with modern technologies</p>
                <div class="flex flex-wrap justify-center gap-4">
                    <span class="tech-badge">TypeScript</span>
                    <span class="tech-badge">Bun</span>
                    <span class="tech-badge">Tailwind CSS</span>
                    <span class="tech-badge">SQLite</span>
                    <span class="tech-badge">Eta</span>
                </div>
            </div>
        </div>
        
        <!-- Scroll Indicator -->
        <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
            <svg class="w-6 h-6 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"/>
            </svg>
        </div>
    </div>
</section>

<!-- Featured Projects Section -->
<section class="py-20 bg-slate-800">
    <div class="container mx-auto px-4">
        <div class="text-center mb-16 fade-in">
            <h2 class="text-4xl font-bold text-white mb-4">Featured Projects</h2>
            <p class="text-xl text-slate-400 max-w-2xl mx-auto">
                A showcase of my recent work and personal projects that demonstrate my skills and passion for development.
            </p>
        </div>
        
        <% if (it.featuredProjects && it.featuredProjects.length > 0) { %>
            <div class="project-grid">
                <% it.featuredProjects.forEach(project => { %>
                    <%~ includeFile('partials/project-card', { project }) %>
                <% }) %>
            </div>
            
            <div class="text-center mt-12 fade-in">
                <a href="/projects" class="btn btn-outline text-lg">
                    View All Projects
                </a>
            </div>
        <% } else { %>
            <div class="text-center py-12">
                <p class="text-slate-400 text-lg">No featured projects available at the moment.</p>
                <a href="/projects" class="btn btn-primary mt-4">
                    View All Projects
                </a>
            </div>
        <% } %>
    </div>
</section>

<!-- About Preview Section -->
<section class="py-20 bg-slate-900">
    <div class="container mx-auto px-4">
        <div class="max-w-4xl mx-auto">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                <!-- Content -->
                <div class="fade-in">
                    <h2 class="text-4xl font-bold text-white mb-6">About Me</h2>
                    <p class="text-lg text-slate-400 mb-6 leading-relaxed">
                        I'm a passionate developer with a love for creating efficient, scalable, and user-friendly applications. 
                        With expertise in modern web technologies, I enjoy turning complex problems into simple, beautiful solutions.
                    </p>
                    <p class="text-lg text-slate-400 mb-8 leading-relaxed">
                        When I'm not coding, you can find me exploring new technologies, contributing to open source projects, 
                        or sharing my knowledge through blog posts and tutorials.
                    </p>
                    <a href="/about" class="btn btn-primary">
                        Learn More About Me
                    </a>
                </div>
                
                <!-- Skills -->
                <div class="fade-in">
                    <h3 class="text-2xl font-semibold text-white mb-6">Skills & Technologies</h3>
                    <div class="space-y-4">
                        <div class="skill-item">
                            <div class="flex justify-between mb-2">
                                <span class="text-slate-300">Frontend Development</span>
                                <span class="text-slate-400">90%</span>
                            </div>
                            <div class="w-full bg-slate-700 rounded-full h-2">
                                <div class="bg-blue-500 h-2 rounded-full glow-effect" style="width: 90%"></div>
                            </div>
                        </div>
                        
                        <div class="skill-item">
                            <div class="flex justify-between mb-2">
                                <span class="text-slate-300">Backend Development</span>
                                <span class="text-slate-400">85%</span>
                            </div>
                            <div class="w-full bg-slate-700 rounded-full h-2">
                                <div class="bg-green-500 h-2 rounded-full glow-effect" style="width: 85%"></div>
                            </div>
                        </div>
                        
                        <div class="skill-item">
                            <div class="flex justify-between mb-2">
                                <span class="text-slate-300">Database Design</span>
                                <span class="text-slate-400">80%</span>
                            </div>
                            <div class="w-full bg-slate-700 rounded-full h-2">
                                <div class="bg-purple-500 h-2 rounded-full glow-effect" style="width: 80%"></div>
                            </div>
                        </div>
                        
                        <div class="skill-item">
                            <div class="flex justify-between mb-2">
                                <span class="text-slate-300">DevOps & Deployment</span>
                                <span class="text-slate-400">75%</span>
                            </div>
                            <div class="w-full bg-slate-700 rounded-full h-2">
                                <div class="bg-yellow-500 h-2 rounded-full glow-effect" style="width: 75%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Latest Blog Posts Section -->
<section class="py-20 bg-slate-800">
    <div class="container mx-auto px-4">
        <div class="text-center mb-16 fade-in">
            <h2 class="text-4xl font-bold text-white mb-4">Latest Blog Posts</h2>
            <p class="text-xl text-slate-400 max-w-2xl mx-auto">
                Thoughts, tutorials, and insights about web development, technology trends, and programming best practices.
            </p>
        </div>
        
        <% if (it.latestPosts && it.latestPosts.length > 0) { %>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <% it.latestPosts.forEach(post => { %>
                    <%~ includeFile('partials/blog-preview', { post }) %>
                <% }) %>
            </div>
            
            <div class="text-center mt-12 fade-in">
                <a href="/blog" class="btn btn-outline text-lg">
                    Read All Posts
                </a>
            </div>
        <% } else { %>
            <div class="text-center py-12">
                <p class="text-slate-400 text-lg">No blog posts available at the moment.</p>
                <a href="/blog" class="btn btn-primary mt-4">
                    Visit Blog
                </a>
            </div>
        <% } %>
    </div>
</section>

<!-- Contact CTA Section -->
<section class="py-20 bg-gradient-to-r from-blue-600 to-purple-600">
    <div class="container mx-auto px-4 text-center">
        <div class="max-w-3xl mx-auto fade-in">
            <h2 class="text-4xl font-bold text-white mb-6">Let's Work Together</h2>
            <p class="text-xl text-blue-100 mb-8 leading-relaxed">
                Have a project in mind? I'm always interested in discussing new opportunities 
                and collaborating on exciting projects.
            </p>
            <a href="/contact" class="btn bg-white text-blue-600 hover:bg-blue-50 text-lg px-8 py-4">
                Start a Conversation
            </a>
        </div>
    </div>
</section>

<style>
.tech-badge {
    @apply inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-slate-800 text-slate-300 border border-slate-700 hover:border-blue-500 hover:text-blue-400 transition-all duration-300 glow-effect;
}

.skill-item {
    @apply transition-all duration-300 hover:transform hover:scale-105;
}
</style>
