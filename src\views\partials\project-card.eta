<div class="card group hover:scale-105 transition-all duration-300 fade-in">
    <!-- Project Image -->
    <% if (it.project.image_url) { %>
        <div class="relative overflow-hidden rounded-lg mb-4">
            <img 
                src="<%= it.project.image_url %>" 
                alt="<%= it.project.title %>"
                class="w-full h-48 object-cover transition-transform duration-300 group-hover:scale-110"
                loading="lazy"
            >
            <div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
        </div>
    <% } %>
    
    <!-- Project Content -->
    <div class="space-y-4">
        <!-- Title and Featured Badge -->
        <div class="flex items-start justify-between">
            <h3 class="text-xl font-semibold text-white group-hover:text-blue-400 transition-colors">
                <%= it.project.title %>
            </h3>
            <% if (it.project.featured) { %>
                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-600 text-white">
                    <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                    </svg>
                    Featured
                </span>
            <% } %>
        </div>
        
        <!-- Description -->
        <p class="text-slate-400 leading-relaxed">
            <%= it.project.description %>
        </p>
        
        <!-- Technologies -->
        <div class="flex flex-wrap gap-2">
            <% it.project.technologies.forEach(tech => { %>
                <span class="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-slate-800 text-slate-300 border border-slate-700">
                    <%= tech %>
                </span>
            <% }) %>
        </div>
        
        <!-- Links -->
        <div class="flex space-x-4 pt-4">
            <% if (it.project.github_url) { %>
                <a 
                    href="<%= it.project.github_url %>" 
                    target="_blank" 
                    rel="noopener noreferrer"
                    class="btn btn-outline flex items-center space-x-2"
                >
                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
                    </svg>
                    <span>Code</span>
                </a>
            <% } %>
            
            <% if (it.project.live_url) { %>
                <a 
                    href="<%= it.project.live_url %>" 
                    target="_blank" 
                    rel="noopener noreferrer"
                    class="btn btn-primary flex items-center space-x-2"
                >
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"/>
                    </svg>
                    <span>Live Demo</span>
                </a>
            <% } %>
        </div>
        
        <!-- Project Meta -->
        <div class="flex items-center justify-between pt-4 border-t border-slate-800 text-sm text-slate-500">
            <span>
                Created: <%= new Date(it.project.created_at).toLocaleDateString('en-US', { 
                    year: 'numeric', 
                    month: 'short', 
                    day: 'numeric' 
                }) %>
            </span>
            <% if (it.showActions) { %>
                <div class="flex space-x-2">
                    <button 
                        onclick="editProject(<%= it.project.id %>)"
                        class="text-blue-400 hover:text-blue-300 transition-colors"
                        title="Edit project"
                    >
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                        </svg>
                    </button>
                    <button 
                        onclick="deleteProject(<%= it.project.id %>)"
                        class="text-red-400 hover:text-red-300 transition-colors"
                        title="Delete project"
                    >
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                        </svg>
                    </button>
                </div>
            <% } %>
        </div>
    </div>
</div>
