<!-- Hero Section -->
<section class="py-20 bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900">
    <div class="container mx-auto px-4">
        <div class="max-w-4xl mx-auto text-center">
            <div class="fade-in">
                <h1 class="text-5xl md:text-6xl font-bold text-white mb-6">
                    About <span class="text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-purple-600">Me</span>
                </h1>
                <p class="text-xl text-slate-300 leading-relaxed">
                    Passionate developer crafting digital experiences with modern technologies
                </p>
            </div>
        </div>
    </div>
</section>

<!-- Main Content -->
<section class="py-20 bg-slate-800">
    <div class="container mx-auto px-4">
        <div class="max-w-6xl mx-auto">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
                <!-- Profile Image -->
                <div class="fade-in">
                    <div class="relative">
                        <div class="w-80 h-80 mx-auto rounded-full bg-gradient-to-br from-blue-500 to-purple-600 p-1">
                            <div class="w-full h-full rounded-full bg-slate-800 flex items-center justify-center">
                                <svg class="w-32 h-32 text-slate-400" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                                </svg>
                            </div>
                        </div>
                        <!-- Floating elements -->
                        <div class="absolute -top-4 -right-4 w-8 h-8 bg-blue-500 rounded-full animate-float"></div>
                        <div class="absolute -bottom-4 -left-4 w-6 h-6 bg-purple-500 rounded-full animate-float" style="animation-delay: 1s;"></div>
                    </div>
                </div>
                
                <!-- About Content -->
                <div class="fade-in">
                    <h2 class="text-3xl font-bold text-white mb-6">Hello, I'm a Developer</h2>
                    <div class="space-y-4 text-slate-300 leading-relaxed">
                        <p>
                            I'm a passionate full-stack developer with a love for creating efficient, scalable, 
                            and user-friendly applications. With several years of experience in web development, 
                            I specialize in modern JavaScript frameworks, backend technologies, and database design.
                        </p>
                        <p>
                            My journey in programming started with curiosity about how websites work, and it has 
                            evolved into a deep passion for solving complex problems through code. I believe in 
                            writing clean, maintainable code and following best practices to deliver high-quality solutions.
                        </p>
                        <p>
                            When I'm not coding, you can find me exploring new technologies, contributing to open 
                            source projects, reading tech blogs, or sharing my knowledge through tutorials and blog posts.
                        </p>
                    </div>
                    
                    <!-- Quick Stats -->
                    <div class="grid grid-cols-2 gap-6 mt-8">
                        <div class="text-center">
                            <div class="text-3xl font-bold text-blue-400 mb-2">50+</div>
                            <div class="text-slate-400">Projects Completed</div>
                        </div>
                        <div class="text-center">
                            <div class="text-3xl font-bold text-purple-400 mb-2">3+</div>
                            <div class="text-slate-400">Years Experience</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Skills Section -->
<section class="py-20 bg-slate-900">
    <div class="container mx-auto px-4">
        <div class="max-w-6xl mx-auto">
            <div class="text-center mb-16 fade-in">
                <h2 class="text-4xl font-bold text-white mb-4">Skills & Technologies</h2>
                <p class="text-xl text-slate-400">
                    Technologies I work with to bring ideas to life
                </p>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- Frontend -->
                <div class="card fade-in">
                    <div class="text-center mb-6">
                        <div class="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                            </svg>
                        </div>
                        <h3 class="text-xl font-semibold text-white mb-4">Frontend Development</h3>
                    </div>
                    <div class="space-y-3">
                        <div class="flex justify-between items-center">
                            <span class="text-slate-300">React/Next.js</span>
                            <div class="w-24 bg-slate-700 rounded-full h-2">
                                <div class="bg-blue-500 h-2 rounded-full" style="width: 90%"></div>
                            </div>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-slate-300">TypeScript</span>
                            <div class="w-24 bg-slate-700 rounded-full h-2">
                                <div class="bg-blue-500 h-2 rounded-full" style="width: 95%"></div>
                            </div>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-slate-300">Tailwind CSS</span>
                            <div class="w-24 bg-slate-700 rounded-full h-2">
                                <div class="bg-blue-500 h-2 rounded-full" style="width: 85%"></div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Backend -->
                <div class="card fade-in">
                    <div class="text-center mb-6">
                        <div class="w-16 h-16 bg-green-600 rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                            </svg>
                        </div>
                        <h3 class="text-xl font-semibold text-white mb-4">Backend Development</h3>
                    </div>
                    <div class="space-y-3">
                        <div class="flex justify-between items-center">
                            <span class="text-slate-300">Node.js/Bun</span>
                            <div class="w-24 bg-slate-700 rounded-full h-2">
                                <div class="bg-green-500 h-2 rounded-full" style="width: 88%"></div>
                            </div>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-slate-300">Express/Fastify</span>
                            <div class="w-24 bg-slate-700 rounded-full h-2">
                                <div class="bg-green-500 h-2 rounded-full" style="width: 85%"></div>
                            </div>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-slate-300">REST APIs</span>
                            <div class="w-24 bg-slate-700 rounded-full h-2">
                                <div class="bg-green-500 h-2 rounded-full" style="width: 92%"></div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Database & Tools -->
                <div class="card fade-in">
                    <div class="text-center mb-6">
                        <div class="w-16 h-16 bg-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M12 3C7.58 3 4 4.79 4 7s3.58 4 8 4 8-1.79 8-4-3.58-4-8-4zM4 9v3c0 2.21 3.58 4 8 4s8-1.79 8-4V9c0 2.21-3.58 4-8 4s-8-1.79-8-4zM4 16v3c0 2.21 3.58 4 8 4s8-1.79 8-4v-3c0 2.21-3.58 4-8 4s-8-1.79-8-4z"/>
                            </svg>
                        </div>
                        <h3 class="text-xl font-semibold text-white mb-4">Database & Tools</h3>
                    </div>
                    <div class="space-y-3">
                        <div class="flex justify-between items-center">
                            <span class="text-slate-300">PostgreSQL/SQLite</span>
                            <div class="w-24 bg-slate-700 rounded-full h-2">
                                <div class="bg-purple-500 h-2 rounded-full" style="width: 80%"></div>
                            </div>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-slate-300">Git/GitHub</span>
                            <div class="w-24 bg-slate-700 rounded-full h-2">
                                <div class="bg-purple-500 h-2 rounded-full" style="width: 95%"></div>
                            </div>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-slate-300">Docker</span>
                            <div class="w-24 bg-slate-700 rounded-full h-2">
                                <div class="bg-purple-500 h-2 rounded-full" style="width: 75%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Experience Timeline -->
<section class="py-20 bg-slate-800">
    <div class="container mx-auto px-4">
        <div class="max-w-4xl mx-auto">
            <div class="text-center mb-16 fade-in">
                <h2 class="text-4xl font-bold text-white mb-4">Experience</h2>
                <p class="text-xl text-slate-400">
                    My journey in software development
                </p>
            </div>
            
            <div class="relative">
                <!-- Timeline line -->
                <div class="absolute left-8 top-0 bottom-0 w-0.5 bg-slate-600"></div>
                
                <div class="space-y-12">
                    <!-- Experience Item 1 -->
                    <div class="relative flex items-start fade-in">
                        <div class="absolute left-6 w-4 h-4 bg-blue-500 rounded-full border-4 border-slate-800"></div>
                        <div class="ml-20">
                            <div class="card">
                                <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-4">
                                    <h3 class="text-xl font-semibold text-white">Full Stack Developer</h3>
                                    <span class="text-blue-400 font-medium">2022 - Present</span>
                                </div>
                                <p class="text-slate-400 mb-4">
                                    Developing modern web applications using React, Node.js, and cloud technologies. 
                                    Focus on creating scalable solutions and improving user experience.
                                </p>
                                <div class="flex flex-wrap gap-2">
                                    <span class="tech-badge">React</span>
                                    <span class="tech-badge">Node.js</span>
                                    <span class="tech-badge">TypeScript</span>
                                    <span class="tech-badge">AWS</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Experience Item 2 -->
                    <div class="relative flex items-start fade-in">
                        <div class="absolute left-6 w-4 h-4 bg-green-500 rounded-full border-4 border-slate-800"></div>
                        <div class="ml-20">
                            <div class="card">
                                <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-4">
                                    <h3 class="text-xl font-semibold text-white">Frontend Developer</h3>
                                    <span class="text-green-400 font-medium">2021 - 2022</span>
                                </div>
                                <p class="text-slate-400 mb-4">
                                    Specialized in creating responsive and interactive user interfaces. 
                                    Collaborated with design teams to implement pixel-perfect designs.
                                </p>
                                <div class="flex flex-wrap gap-2">
                                    <span class="tech-badge">Vue.js</span>
                                    <span class="tech-badge">JavaScript</span>
                                    <span class="tech-badge">CSS/SCSS</span>
                                    <span class="tech-badge">Figma</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Experience Item 3 -->
                    <div class="relative flex items-start fade-in">
                        <div class="absolute left-6 w-4 h-4 bg-purple-500 rounded-full border-4 border-slate-800"></div>
                        <div class="ml-20">
                            <div class="card">
                                <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-4">
                                    <h3 class="text-xl font-semibold text-white">Junior Developer</h3>
                                    <span class="text-purple-400 font-medium">2020 - 2021</span>
                                </div>
                                <p class="text-slate-400 mb-4">
                                    Started my professional journey learning web development fundamentals. 
                                    Built small applications and contributed to team projects.
                                </p>
                                <div class="flex flex-wrap gap-2">
                                    <span class="tech-badge">HTML/CSS</span>
                                    <span class="tech-badge">JavaScript</span>
                                    <span class="tech-badge">PHP</span>
                                    <span class="tech-badge">MySQL</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="py-20 bg-gradient-to-r from-blue-600 to-purple-600">
    <div class="container mx-auto px-4 text-center">
        <div class="max-w-3xl mx-auto fade-in">
            <h2 class="text-4xl font-bold text-white mb-6">Let's Build Something Amazing</h2>
            <p class="text-xl text-blue-100 mb-8 leading-relaxed">
                I'm always excited to work on new projects and collaborate with talented people. 
                Let's discuss how we can bring your ideas to life.
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="/contact" class="btn bg-white text-blue-600 hover:bg-blue-50 text-lg px-8 py-4">
                    Get In Touch
                </a>
                <a href="/projects" class="btn border-white text-white hover:bg-white hover:text-blue-600 text-lg px-8 py-4">
                    View My Work
                </a>
            </div>
        </div>
    </div>
</section>

<style>
.tech-badge {
    @apply inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-slate-700 text-slate-300 border border-slate-600;
}
</style>
