@import "tailwindcss";

// Custom CSS variables for theming
:root {
  --color-bg-primary: #0f172a;
  --color-bg-secondary: #1e293b;
  --color-text-primary: #f8fafc;
  --color-text-secondary: #cbd5e1;
  --color-accent: #60a5fa;
  --color-accent-hover: #3b82f6;
  --glow-color: rgba(96, 165, 250, 0.5);
  --border-radius: 0.5rem;
  --transition: all 0.3s ease;
}

// Light theme
[data-theme="light"] {
  --color-bg-primary: #ffffff;
  --color-bg-secondary: #f8fafc;
  --color-text-primary: #1e293b;
  --color-text-secondary: #475569;
  --color-accent: #3b82f6;
  --color-accent-hover: #2563eb;
  --glow-color: rgba(59, 130, 246, 0.3);
}

// Base styles
* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: 'JetBrains Mono', 'Fira Code', monospace;
  background-color: var(--color-bg-primary);
  color: var(--color-text-primary);
  line-height: 1.6;
  transition: var(--transition);
}

// Custom components
.glow-effect {
  transition: var(--transition);
  
  &:hover {
    box-shadow: 0 0 20px var(--glow-color);
    transform: translateY(-2px);
  }
}

.card {
  background: var(--color-bg-secondary);
  border-radius: var(--border-radius);
  padding: 1.5rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: var(--transition);
  
  &:hover {
    border-color: var(--color-accent);
    box-shadow: 0 0 15px var(--glow-color);
  }
}

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1.5rem;
  border-radius: var(--border-radius);
  font-weight: 500;
  text-decoration: none;
  transition: var(--transition);
  border: 1px solid transparent;
  cursor: pointer;
  font-family: inherit;
  
  &.btn-primary {
    background: var(--color-accent);
    color: white;
    
    &:hover {
      background: var(--color-accent-hover);
      box-shadow: 0 0 15px var(--glow-color);
      transform: translateY(-1px);
    }
  }
  
  &.btn-outline {
    border-color: var(--color-accent);
    color: var(--color-accent);
    
    &:hover {
      background: var(--color-accent);
      color: white;
      box-shadow: 0 0 15px var(--glow-color);
    }
  }
  
  &.btn-ghost {
    color: var(--color-text-secondary);
    
    &:hover {
      color: var(--color-accent);
      background: rgba(96, 165, 250, 0.1);
    }
  }
}

// Navigation styles
.nav-link {
  position: relative;
  color: var(--color-text-secondary);
  text-decoration: none;
  padding: 0.5rem 1rem;
  border-radius: var(--border-radius);
  transition: var(--transition);
  
  &:hover,
  &.active {
    color: var(--color-accent);
    background: rgba(96, 165, 250, 0.1);
  }
  
  &::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 50%;
    width: 0;
    height: 2px;
    background: var(--color-accent);
    transition: var(--transition);
    transform: translateX(-50%);
  }
  
  &:hover::after,
  &.active::after {
    width: 80%;
  }
}

// Form styles
.form-group {
  margin-bottom: 1.5rem;
  
  label {
    display: block;
    margin-bottom: 0.5rem;
    color: var(--color-text-primary);
    font-weight: 500;
  }
  
  input,
  textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--border-radius);
    background: var(--color-bg-secondary);
    color: var(--color-text-primary);
    font-family: inherit;
    transition: var(--transition);
    
    &:focus {
      outline: none;
      border-color: var(--color-accent);
      box-shadow: 0 0 10px var(--glow-color);
    }
    
    &::placeholder {
      color: var(--color-text-secondary);
    }
  }
  
  textarea {
    resize: vertical;
    min-height: 120px;
  }
}

// Code blocks
pre,
code {
  font-family: 'JetBrains Mono', 'Fira Code', monospace;
}

pre {
  background: var(--color-bg-secondary);
  padding: 1rem;
  border-radius: var(--border-radius);
  overflow-x: auto;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

code {
  background: rgba(96, 165, 250, 0.1);
  padding: 0.2rem 0.4rem;
  border-radius: 0.25rem;
  font-size: 0.9em;
}

// Responsive utilities
@media (max-width: 768px) {
  .card {
    padding: 1rem;
  }
  
  .btn {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
  }
}

// Loading animation
.loading {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid rgba(96, 165, 250, 0.3);
  border-radius: 50%;
  border-top-color: var(--color-accent);
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

// Scroll animations
.fade-in {
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 0.6s ease, transform 0.6s ease;
  
  &.visible {
    opacity: 1;
    transform: translateY(0);
  }
}

// Project grid
.project-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

// Blog post styles
.blog-content {
  h1, h2, h3, h4, h5, h6 {
    color: var(--color-text-primary);
    margin-top: 2rem;
    margin-bottom: 1rem;
    
    &:first-child {
      margin-top: 0;
    }
  }
  
  p {
    margin-bottom: 1rem;
    color: var(--color-text-secondary);
  }
  
  a {
    color: var(--color-accent);
    text-decoration: none;
    
    &:hover {
      text-decoration: underline;
    }
  }
  
  ul, ol {
    margin-bottom: 1rem;
    padding-left: 2rem;
    
    li {
      margin-bottom: 0.5rem;
      color: var(--color-text-secondary);
    }
  }
  
  blockquote {
    border-left: 4px solid var(--color-accent);
    padding-left: 1rem;
    margin: 1.5rem 0;
    font-style: italic;
    color: var(--color-text-secondary);
  }
}
