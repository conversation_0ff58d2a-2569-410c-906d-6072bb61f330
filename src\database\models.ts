import { getDatabase } from './connection';
import type { 
  Project, 
  CreateProject, 
  UpdateProject,
  BlogPost,
  CreateBlogPost,
  UpdateBlogPost,
  ContactForm,
  Pagination
} from '@/types';

// Project Model
export class ProjectModel {
  static async findAll(options: { 
    featured?: boolean; 
    limit?: number; 
    offset?: number; 
  } = {}): Promise<Project[]> {
    const db = getDatabase();
    let sql = 'SELECT * FROM projects';
    const params: any[] = [];
    
    if (options.featured !== undefined) {
      sql += ' WHERE featured = ?';
      params.push(options.featured);
    }
    
    sql += ' ORDER BY created_at DESC';
    
    if (options.limit) {
      sql += ' LIMIT ?';
      params.push(options.limit);
      
      if (options.offset) {
        sql += ' OFFSET ?';
        params.push(options.offset);
      }
    }
    
    const projects = await db.query(sql, params);
    return projects.map(this.transformProject);
  }

  static async findById(id: number): Promise<Project | null> {
    const db = getDatabase();
    const project = await db.get('SELECT * FROM projects WHERE id = ?', [id]);
    return project ? this.transformProject(project) : null;
  }

  static async create(data: CreateProject): Promise<Project> {
    const db = getDatabase();
    const result = await db.run(
      `INSERT INTO projects (title, description, technologies, github_url, live_url, image_url, featured)
       VALUES (?, ?, ?, ?, ?, ?, ?)`,
      [
        data.title,
        data.description,
        JSON.stringify(data.technologies),
        data.github_url,
        data.live_url,
        data.image_url,
        data.featured
      ]
    );
    
    const project = await this.findById(result.lastInsertRowid as number);
    if (!project) throw new Error('Failed to create project');
    return project;
  }

  static async update(id: number, data: UpdateProject): Promise<Project | null> {
    const db = getDatabase();
    const updates: string[] = [];
    const params: any[] = [];
    
    Object.entries(data).forEach(([key, value]) => {
      if (value !== undefined) {
        if (key === 'technologies') {
          updates.push(`${key} = ?`);
          params.push(JSON.stringify(value));
        } else {
          updates.push(`${key} = ?`);
          params.push(value);
        }
      }
    });
    
    if (updates.length === 0) return this.findById(id);
    
    params.push(id);
    await db.run(
      `UPDATE projects SET ${updates.join(', ')} WHERE id = ?`,
      params
    );
    
    return this.findById(id);
  }

  static async delete(id: number): Promise<boolean> {
    const db = getDatabase();
    const result = await db.run('DELETE FROM projects WHERE id = ?', [id]);
    return result.changes > 0;
  }

  static async count(featured?: boolean): Promise<number> {
    const db = getDatabase();
    let sql = 'SELECT COUNT(*) as count FROM projects';
    const params: any[] = [];
    
    if (featured !== undefined) {
      sql += ' WHERE featured = ?';
      params.push(featured);
    }
    
    const result = await db.get(sql, params);
    return result.count;
  }

  private static transformProject(row: any): Project {
    return {
      ...row,
      technologies: JSON.parse(row.technologies),
      featured: Boolean(row.featured)
    };
  }
}

// Blog Post Model
export class BlogPostModel {
  static async findAll(options: {
    published?: boolean;
    featured?: boolean;
    limit?: number;
    offset?: number;
  } = {}): Promise<BlogPost[]> {
    const db = getDatabase();
    let sql = 'SELECT * FROM blog_posts';
    const conditions: string[] = [];
    const params: any[] = [];
    
    if (options.published !== undefined) {
      conditions.push('published = ?');
      params.push(options.published);
    }
    
    if (options.featured !== undefined) {
      conditions.push('featured = ?');
      params.push(options.featured);
    }
    
    if (conditions.length > 0) {
      sql += ' WHERE ' + conditions.join(' AND ');
    }
    
    sql += ' ORDER BY created_at DESC';
    
    if (options.limit) {
      sql += ' LIMIT ?';
      params.push(options.limit);
      
      if (options.offset) {
        sql += ' OFFSET ?';
        params.push(options.offset);
      }
    }
    
    const posts = await db.query(sql, params);
    return posts.map(this.transformBlogPost);
  }

  static async findBySlug(slug: string): Promise<BlogPost | null> {
    const db = getDatabase();
    const post = await db.get('SELECT * FROM blog_posts WHERE slug = ?', [slug]);
    return post ? this.transformBlogPost(post) : null;
  }

  static async findById(id: number): Promise<BlogPost | null> {
    const db = getDatabase();
    const post = await db.get('SELECT * FROM blog_posts WHERE id = ?', [id]);
    return post ? this.transformBlogPost(post) : null;
  }

  static async create(data: CreateBlogPost): Promise<BlogPost> {
    const db = getDatabase();
    const publishedAt = data.published ? new Date().toISOString() : null;
    
    const result = await db.run(
      `INSERT INTO blog_posts (title, slug, content, excerpt, published, featured, tags, published_at)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        data.title,
        data.slug,
        data.content,
        data.excerpt,
        data.published,
        data.featured,
        JSON.stringify(data.tags),
        publishedAt
      ]
    );
    
    const post = await this.findById(result.lastInsertRowid as number);
    if (!post) throw new Error('Failed to create blog post');
    return post;
  }

  static async update(id: number, data: UpdateBlogPost): Promise<BlogPost | null> {
    const db = getDatabase();
    const updates: string[] = [];
    const params: any[] = [];
    
    Object.entries(data).forEach(([key, value]) => {
      if (value !== undefined) {
        if (key === 'tags') {
          updates.push(`${key} = ?`);
          params.push(JSON.stringify(value));
        } else {
          updates.push(`${key} = ?`);
          params.push(value);
        }
      }
    });
    
    if (updates.length === 0) return this.findById(id);
    
    params.push(id);
    await db.run(
      `UPDATE blog_posts SET ${updates.join(', ')} WHERE id = ?`,
      params
    );
    
    return this.findById(id);
  }

  static async delete(id: number): Promise<boolean> {
    const db = getDatabase();
    const result = await db.run('DELETE FROM blog_posts WHERE id = ?', [id]);
    return result.changes > 0;
  }

  static async count(published?: boolean): Promise<number> {
    const db = getDatabase();
    let sql = 'SELECT COUNT(*) as count FROM blog_posts';
    const params: any[] = [];
    
    if (published !== undefined) {
      sql += ' WHERE published = ?';
      params.push(published);
    }
    
    const result = await db.get(sql, params);
    return result.count;
  }

  private static transformBlogPost(row: any): BlogPost {
    return {
      ...row,
      tags: JSON.parse(row.tags),
      published: Boolean(row.published),
      featured: Boolean(row.featured)
    };
  }
}

// Contact Model
export class ContactModel {
  static async create(data: ContactForm): Promise<void> {
    const db = getDatabase();
    await db.run(
      `INSERT INTO contact_submissions (name, email, subject, message)
       VALUES (?, ?, ?, ?)`,
      [data.name, data.email, data.subject, data.message]
    );
  }

  static async findAll(options: {
    read?: boolean;
    limit?: number;
    offset?: number;
  } = {}): Promise<any[]> {
    const db = getDatabase();
    let sql = 'SELECT * FROM contact_submissions';
    const params: any[] = [];
    
    if (options.read !== undefined) {
      sql += ' WHERE read = ?';
      params.push(options.read);
    }
    
    sql += ' ORDER BY created_at DESC';
    
    if (options.limit) {
      sql += ' LIMIT ?';
      params.push(options.limit);
      
      if (options.offset) {
        sql += ' OFFSET ?';
        params.push(options.offset);
      }
    }
    
    return await db.query(sql, params);
  }

  static async markAsRead(id: number): Promise<void> {
    const db = getDatabase();
    await db.run('UPDATE contact_submissions SET read = 1 WHERE id = ?', [id]);
  }
}
