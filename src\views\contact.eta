<!-- Hero Section -->
<section class="py-20 bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900">
    <div class="container mx-auto px-4">
        <div class="max-w-4xl mx-auto text-center">
            <div class="fade-in">
                <h1 class="text-5xl md:text-6xl font-bold text-white mb-6">
                    Get In <span class="text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-purple-600">Touch</span>
                </h1>
                <p class="text-xl text-slate-300 leading-relaxed">
                    Have a project in mind? Let's discuss how we can work together to bring your ideas to life.
                </p>
            </div>
        </div>
    </div>
</section>

<!-- Contact Section -->
<section class="py-20 bg-slate-800">
    <div class="container mx-auto px-4">
        <div class="max-w-6xl mx-auto">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-16">
                <!-- Contact Form -->
                <div class="fade-in">
                    <div class="card">
                        <h2 class="text-3xl font-bold text-white mb-6">Send Me a Message</h2>
                        <p class="text-slate-400 mb-8">
                            Fill out the form below and I'll get back to you as soon as possible.
                        </p>
                        
                        <form action="/contact" method="POST" data-ajax class="space-y-6">
                            <div class="form-group">
                                <label for="name">Name *</label>
                                <input 
                                    type="text" 
                                    id="name" 
                                    name="name" 
                                    required
                                    placeholder="Your full name"
                                >
                            </div>
                            
                            <div class="form-group">
                                <label for="email">Email *</label>
                                <input 
                                    type="email" 
                                    id="email" 
                                    name="email" 
                                    required
                                    placeholder="<EMAIL>"
                                >
                            </div>
                            
                            <div class="form-group">
                                <label for="subject">Subject *</label>
                                <input 
                                    type="text" 
                                    id="subject" 
                                    name="subject" 
                                    required
                                    placeholder="What's this about?"
                                >
                            </div>
                            
                            <div class="form-group">
                                <label for="message">Message *</label>
                                <textarea 
                                    id="message" 
                                    name="message" 
                                    required
                                    placeholder="Tell me about your project or question..."
                                    rows="6"
                                ></textarea>
                            </div>
                            
                            <button type="submit" class="btn btn-primary w-full">
                                Send Message
                            </button>
                        </form>
                    </div>
                </div>
                
                <!-- Contact Information -->
                <div class="space-y-8 fade-in">
                    <!-- Contact Info -->
                    <div class="card">
                        <h3 class="text-2xl font-bold text-white mb-6">Contact Information</h3>
                        
                        <div class="space-y-6">
                            <div class="flex items-start space-x-4">
                                <div class="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center flex-shrink-0">
                                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
                                    </svg>
                                </div>
                                <div>
                                    <h4 class="text-lg font-semibold text-white mb-1">Email</h4>
                                    <p class="text-slate-400"><EMAIL></p>
                                    <p class="text-sm text-slate-500">I'll respond within 24 hours</p>
                                </div>
                            </div>
                            
                            <div class="flex items-start space-x-4">
                                <div class="w-12 h-12 bg-green-600 rounded-lg flex items-center justify-center flex-shrink-0">
                                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"/>
                                    </svg>
                                </div>
                                <div>
                                    <h4 class="text-lg font-semibold text-white mb-1">Location</h4>
                                    <p class="text-slate-400">Remote / Worldwide</p>
                                    <p class="text-sm text-slate-500">Available for remote work</p>
                                </div>
                            </div>
                            
                            <div class="flex items-start space-x-4">
                                <div class="w-12 h-12 bg-purple-600 rounded-lg flex items-center justify-center flex-shrink-0">
                                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                    </svg>
                                </div>
                                <div>
                                    <h4 class="text-lg font-semibold text-white mb-1">Response Time</h4>
                                    <p class="text-slate-400">Within 24 hours</p>
                                    <p class="text-sm text-slate-500">Usually much faster!</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Social Links -->
                    <div class="card">
                        <h3 class="text-2xl font-bold text-white mb-6">Connect With Me</h3>
                        
                        <div class="grid grid-cols-2 gap-4">
                            <a 
                                href="https://github.com" 
                                target="_blank" 
                                rel="noopener noreferrer"
                                class="flex items-center space-x-3 p-4 bg-slate-700 rounded-lg hover:bg-slate-600 transition-colors glow-effect"
                            >
                                <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
                                </svg>
                                <span class="text-white font-medium">GitHub</span>
                            </a>
                            
                            <a 
                                href="https://linkedin.com" 
                                target="_blank" 
                                rel="noopener noreferrer"
                                class="flex items-center space-x-3 p-4 bg-slate-700 rounded-lg hover:bg-slate-600 transition-colors glow-effect"
                            >
                                <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                                </svg>
                                <span class="text-white font-medium">LinkedIn</span>
                            </a>
                            
                            <a 
                                href="https://twitter.com" 
                                target="_blank" 
                                rel="noopener noreferrer"
                                class="flex items-center space-x-3 p-4 bg-slate-700 rounded-lg hover:bg-slate-600 transition-colors glow-effect"
                            >
                                <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                                </svg>
                                <span class="text-white font-medium">Twitter</span>
                            </a>
                            
                            <a 
                                href="mailto:<EMAIL>"
                                class="flex items-center space-x-3 p-4 bg-slate-700 rounded-lg hover:bg-slate-600 transition-colors glow-effect"
                            >
                                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
                                </svg>
                                <span class="text-white font-medium">Email</span>
                            </a>
                        </div>
                    </div>
                    
                    <!-- Availability -->
                    <div class="card">
                        <h3 class="text-2xl font-bold text-white mb-6">Current Availability</h3>
                        
                        <div class="flex items-center space-x-3 mb-4">
                            <div class="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                            <span class="text-green-400 font-medium">Available for new projects</span>
                        </div>
                        
                        <p class="text-slate-400 mb-6">
                            I'm currently accepting new projects and collaborations. 
                            Let's discuss your requirements and see how I can help.
                        </p>
                        
                        <div class="space-y-3 text-sm">
                            <div class="flex justify-between">
                                <span class="text-slate-400">Response time:</span>
                                <span class="text-white">Within 24 hours</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-slate-400">Project start:</span>
                                <span class="text-white">1-2 weeks</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-slate-400">Preferred project length:</span>
                                <span class="text-white">2-12 weeks</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- FAQ Section -->
<section class="py-20 bg-slate-900">
    <div class="container mx-auto px-4">
        <div class="max-w-4xl mx-auto">
            <div class="text-center mb-16 fade-in">
                <h2 class="text-4xl font-bold text-white mb-4">Frequently Asked Questions</h2>
                <p class="text-xl text-slate-400">
                    Common questions about working together
                </p>
            </div>
            
            <div class="space-y-6">
                <div class="card fade-in">
                    <h3 class="text-xl font-semibold text-white mb-3">What types of projects do you work on?</h3>
                    <p class="text-slate-400">
                        I specialize in full-stack web applications, APIs, and modern frontend interfaces. 
                        I work with technologies like React, Node.js, TypeScript, and various databases.
                    </p>
                </div>
                
                <div class="card fade-in">
                    <h3 class="text-xl font-semibold text-white mb-3">What's your typical project timeline?</h3>
                    <p class="text-slate-400">
                        Project timelines vary based on complexity, but most projects range from 2-12 weeks. 
                        I provide detailed estimates after understanding your requirements.
                    </p>
                </div>
                
                <div class="card fade-in">
                    <h3 class="text-xl font-semibold text-white mb-3">Do you work with teams or solo?</h3>
                    <p class="text-slate-400">
                        I'm comfortable working both independently and as part of a team. 
                        I have experience collaborating with designers, other developers, and project managers.
                    </p>
                </div>
                
                <div class="card fade-in">
                    <h3 class="text-xl font-semibold text-white mb-3">What's included in your development process?</h3>
                    <p class="text-slate-400">
                        My process includes planning, design review, development, testing, deployment, 
                        and post-launch support. I maintain clear communication throughout the project.
                    </p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="py-20 bg-gradient-to-r from-blue-600 to-purple-600">
    <div class="container mx-auto px-4 text-center">
        <div class="max-w-3xl mx-auto fade-in">
            <h2 class="text-4xl font-bold text-white mb-6">Ready to Start Your Project?</h2>
            <p class="text-xl text-blue-100 mb-8 leading-relaxed">
                Let's turn your ideas into reality. I'm here to help you build something amazing.
            </p>
            <a href="#contact-form" class="btn bg-white text-blue-600 hover:bg-blue-50 text-lg px-8 py-4">
                Send Me a Message
            </a>
        </div>
    </div>
</section>

<script>
// Smooth scroll to contact form
document.addEventListener('DOMContentLoaded', function() {
    const ctaButton = document.querySelector('a[href="#contact-form"]');
    if (ctaButton) {
        ctaButton.addEventListener('click', function(e) {
            e.preventDefault();
            const form = document.querySelector('form[data-ajax]');
            if (form) {
                form.scrollIntoView({ behavior: 'smooth', block: 'start' });
                // Focus on the first input
                setTimeout(() => {
                    const firstInput = form.querySelector('input[type="text"]');
                    if (firstInput) firstInput.focus();
                }, 500);
            }
        });
    }
});
</script>
