import type { <PERSON><PERSON><PERSON><PERSON>, RouteContext } from '@/types';
import { ProjectModel, BlogPostModel, ContactModel } from '@/database/models';
import { 
  createSuccessResponse, 
  createErrorResponse, 
  generateSlug,
  checkRateLimit 
} from '@/utils';
import { 
  ProjectSchema, 
  CreateProjectSchema, 
  UpdateProjectSchema,
  BlogPostSchema,
  CreateBlogPostSchema,
  UpdateBlogPostSchema,
  ContactFormSchema 
} from '@/types';
import { renderView } from './view-engine';

// Home page
export const homeHandler: RouteHandler = async (context: RouteContext) => {
  try {
    // Get featured projects and latest blog posts
    const [featuredProjects, latestPosts] = await Promise.all([
      ProjectModel.findAll({ featured: true, limit: 6 }),
      BlogPostModel.findAll({ published: true, limit: 3 })
    ]);

    return renderView('home', {
      title: 'Home',
      description: 'Welcome to my portfolio - showcasing modern web development projects and insights',
      currentPath: context.request.url,
      featuredProjects,
      latestPosts
    });
  } catch (error) {
    console.error('Home page error:', error);
    return createErrorResponse('Failed to load home page', 500);
  }
};

// About page
export const aboutHandler: RouteHandler = async (context: RouteContext) => {
  return renderView('about', {
    title: 'About',
    description: 'Learn more about my background, skills, and experience in web development',
    currentPath: context.request.url
  });
};

// Projects page
export const projectsHandler: RouteHandler = async (context: RouteContext) => {
  try {
    const page = parseInt(context.query.page || '1');
    const limit = 12;
    const offset = (page - 1) * limit;

    const [projects, totalCount] = await Promise.all([
      ProjectModel.findAll({ limit, offset }),
      ProjectModel.count()
    ]);

    const totalPages = Math.ceil(totalCount / limit);

    return renderView('projects', {
      title: 'Projects',
      description: 'A collection of my web development projects and applications',
      currentPath: context.request.url,
      projects,
      pagination: {
        page,
        limit,
        total: totalCount,
        totalPages
      }
    });
  } catch (error) {
    console.error('Projects page error:', error);
    return createErrorResponse('Failed to load projects', 500);
  }
};

// Blog page
export const blogHandler: RouteHandler = async (context: RouteContext) => {
  try {
    const page = parseInt(context.query.page || '1');
    const limit = 10;
    const offset = (page - 1) * limit;

    const [posts, totalCount] = await Promise.all([
      BlogPostModel.findAll({ published: true, limit, offset }),
      BlogPostModel.count(true)
    ]);

    const totalPages = Math.ceil(totalCount / limit);

    return renderView('blog', {
      title: 'Blog',
      description: 'Thoughts, tutorials, and insights about web development and technology',
      currentPath: context.request.url,
      posts,
      pagination: {
        page,
        limit,
        total: totalCount,
        totalPages
      }
    });
  } catch (error) {
    console.error('Blog page error:', error);
    return createErrorResponse('Failed to load blog posts', 500);
  }
};

// Individual blog post
export const blogPostHandler: RouteHandler = async (context: RouteContext) => {
  try {
    const { slug } = context.params;
    const post = await BlogPostModel.findBySlug(slug);

    if (!post || !post.published) {
      return new Response('Post not found', { status: 404 });
    }

    return renderView('blog-post', {
      title: post.title,
      description: post.excerpt || `Read ${post.title} on my blog`,
      currentPath: context.request.url,
      post
    });
  } catch (error) {
    console.error('Blog post error:', error);
    return createErrorResponse('Failed to load blog post', 500);
  }
};

// Contact page
export const contactHandler: RouteHandler = async (context: RouteContext) => {
  return renderView('contact', {
    title: 'Contact',
    description: 'Get in touch with me for project collaborations and opportunities',
    currentPath: context.request.url
  });
};

// Contact form submission
export const contactSubmitHandler: RouteHandler = async (context: RouteContext) => {
  try {
    // Rate limiting
    const clientIP = context.request.headers.get('x-forwarded-for') || 'unknown';
    if (!checkRateLimit(`contact:${clientIP}`, 5, 60000)) { // 5 requests per minute
      return createErrorResponse('Too many requests. Please try again later.', 429);
    }

    const formData = await context.request.formData();
    const data = {
      name: formData.get('name') as string,
      email: formData.get('email') as string,
      subject: formData.get('subject') as string,
      message: formData.get('message') as string
    };

    // Validate data
    const validation = ContactFormSchema(data);
    if (validation instanceof Error) {
      return createErrorResponse('Invalid form data', 400, validation.message);
    }

    // Save to database
    await ContactModel.create(data);

    return createSuccessResponse(null, 'Message sent successfully!');
  } catch (error) {
    console.error('Contact form error:', error);
    return createErrorResponse('Failed to send message', 500);
  }
};

// API Routes for Projects
export const apiProjectsHandler: RouteHandler = async (context: RouteContext) => {
  if (context.request.method === 'GET') {
    try {
      const page = parseInt(context.query.page || '1');
      const limit = parseInt(context.query.limit || '10');
      const featured = context.query.featured === 'true' ? true : undefined;
      const offset = (page - 1) * limit;

      const [projects, totalCount] = await Promise.all([
        ProjectModel.findAll({ featured, limit, offset }),
        ProjectModel.count(featured)
      ]);

      return createSuccessResponse({
        projects,
        pagination: {
          page,
          limit,
          total: totalCount,
          totalPages: Math.ceil(totalCount / limit)
        }
      });
    } catch (error) {
      return createErrorResponse('Failed to fetch projects', 500);
    }
  }

  if (context.request.method === 'POST') {
    try {
      const data = await context.request.json();
      
      // Validate data
      const validation = CreateProjectSchema(data);
      if (validation instanceof Error) {
        return createErrorResponse('Invalid project data', 400, validation.message);
      }

      const project = await ProjectModel.create(data);
      return createSuccessResponse(project, 'Project created successfully');
    } catch (error) {
      return createErrorResponse('Failed to create project', 500);
    }
  }

  return createErrorResponse('Method not allowed', 405);
};

export const apiProjectHandler: RouteHandler = async (context: RouteContext) => {
  const { id } = context.params;
  const projectId = parseInt(id);

  if (context.request.method === 'GET') {
    try {
      const project = await ProjectModel.findById(projectId);
      if (!project) {
        return createErrorResponse('Project not found', 404);
      }
      return createSuccessResponse(project);
    } catch (error) {
      return createErrorResponse('Failed to fetch project', 500);
    }
  }

  if (context.request.method === 'PUT') {
    try {
      const data = await context.request.json();
      
      // Validate data
      const validation = UpdateProjectSchema(data);
      if (validation instanceof Error) {
        return createErrorResponse('Invalid project data', 400, validation.message);
      }

      const project = await ProjectModel.update(projectId, data);
      if (!project) {
        return createErrorResponse('Project not found', 404);
      }
      
      return createSuccessResponse(project, 'Project updated successfully');
    } catch (error) {
      return createErrorResponse('Failed to update project', 500);
    }
  }

  if (context.request.method === 'DELETE') {
    try {
      const deleted = await ProjectModel.delete(projectId);
      if (!deleted) {
        return createErrorResponse('Project not found', 404);
      }
      
      return createSuccessResponse(null, 'Project deleted successfully');
    } catch (error) {
      return createErrorResponse('Failed to delete project', 500);
    }
  }

  return createErrorResponse('Method not allowed', 405);
};

// API Routes for Blog Posts
export const apiBlogPostsHandler: RouteHandler = async (context: RouteContext) => {
  if (context.request.method === 'GET') {
    try {
      const page = parseInt(context.query.page || '1');
      const limit = parseInt(context.query.limit || '10');
      const published = context.query.published === 'true' ? true : undefined;
      const offset = (page - 1) * limit;

      const [posts, totalCount] = await Promise.all([
        BlogPostModel.findAll({ published, limit, offset }),
        BlogPostModel.count(published)
      ]);

      return createSuccessResponse({
        posts,
        pagination: {
          page,
          limit,
          total: totalCount,
          totalPages: Math.ceil(totalCount / limit)
        }
      });
    } catch (error) {
      return createErrorResponse('Failed to fetch blog posts', 500);
    }
  }

  if (context.request.method === 'POST') {
    try {
      const data = await context.request.json();
      
      // Generate slug if not provided
      if (!data.slug) {
        data.slug = generateSlug(data.title);
      }
      
      // Validate data
      const validation = CreateBlogPostSchema(data);
      if (validation instanceof Error) {
        return createErrorResponse('Invalid blog post data', 400, validation.message);
      }

      const post = await BlogPostModel.create(data);
      return createSuccessResponse(post, 'Blog post created successfully');
    } catch (error) {
      return createErrorResponse('Failed to create blog post', 500);
    }
  }

  return createErrorResponse('Method not allowed', 405);
};
