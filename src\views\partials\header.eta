<header class="sticky top-0 z-40 bg-slate-900/95 backdrop-blur-sm border-b border-slate-800">
    <nav class="container mx-auto px-4 py-4">
        <div class="flex items-center justify-between">
            <!-- Logo -->
            <a href="/" class="text-xl font-bold text-blue-400 hover:text-blue-300 transition-colors glow-effect">
                &lt;Portfolio /&gt;
            </a>
            
            <!-- Desktop Navigation -->
            <div class="hidden md:flex items-center space-x-8">
                <a href="/" class="nav-link <%= it.currentPath === '/' ? 'active' : '' %>">
                    Home
                </a>
                <a href="/about" class="nav-link <%= it.currentPath === '/about' ? 'active' : '' %>">
                    About
                </a>
                <a href="/projects" class="nav-link <%= it.currentPath === '/projects' ? 'active' : '' %>">
                    Projects
                </a>
                <a href="/blog" class="nav-link <%= it.currentPath === '/blog' ? 'active' : '' %>">
                    Blog
                </a>
                <a href="/contact" class="nav-link <%= it.currentPath === '/contact' ? 'active' : '' %>">
                    Contact
                </a>
                
                <!-- Theme Toggle -->
                <button 
                    onclick="toggleTheme()" 
                    class="p-2 rounded-lg bg-slate-800 hover:bg-slate-700 transition-colors glow-effect"
                    aria-label="Toggle theme"
                >
                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                        <path class="dark:hidden" fill-rule="evenodd" d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" clip-rule="evenodd"></path>
                        <path class="hidden dark:block" d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z"></path>
                    </svg>
                </button>
            </div>
            
            <!-- Mobile Menu Button -->
            <button 
                class="md:hidden p-2 rounded-lg bg-slate-800 hover:bg-slate-700 transition-colors"
                onclick="toggleMobileMenu()"
                data-mobile-menu-toggle
                aria-label="Toggle mobile menu"
            >
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                </svg>
            </button>
        </div>
    </nav>
    
    <!-- Mobile Navigation -->
    <div 
        id="mobile-menu" 
        class="md:hidden fixed inset-y-0 right-0 w-64 bg-slate-900 border-l border-slate-800 transform translate-x-full transition-transform duration-300 ease-in-out z-50"
    >
        <div class="p-4">
            <div class="flex items-center justify-between mb-8">
                <span class="text-lg font-bold text-blue-400">&lt;Menu /&gt;</span>
                <button 
                    onclick="toggleMobileMenu()"
                    class="p-2 rounded-lg bg-slate-800 hover:bg-slate-700 transition-colors"
                    aria-label="Close mobile menu"
                >
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
            
            <nav class="space-y-4">
                <a 
                    href="/" 
                    class="block py-3 px-4 rounded-lg hover:bg-slate-800 transition-colors <%= it.currentPath === '/' ? 'bg-slate-800 text-blue-400' : '' %>"
                    onclick="toggleMobileMenu()"
                >
                    Home
                </a>
                <a 
                    href="/about" 
                    class="block py-3 px-4 rounded-lg hover:bg-slate-800 transition-colors <%= it.currentPath === '/about' ? 'bg-slate-800 text-blue-400' : '' %>"
                    onclick="toggleMobileMenu()"
                >
                    About
                </a>
                <a 
                    href="/projects" 
                    class="block py-3 px-4 rounded-lg hover:bg-slate-800 transition-colors <%= it.currentPath === '/projects' ? 'bg-slate-800 text-blue-400' : '' %>"
                    onclick="toggleMobileMenu()"
                >
                    Projects
                </a>
                <a 
                    href="/blog" 
                    class="block py-3 px-4 rounded-lg hover:bg-slate-800 transition-colors <%= it.currentPath === '/blog' ? 'bg-slate-800 text-blue-400' : '' %>"
                    onclick="toggleMobileMenu()"
                >
                    Blog
                </a>
                <a 
                    href="/contact" 
                    class="block py-3 px-4 rounded-lg hover:bg-slate-800 transition-colors <%= it.currentPath === '/contact' ? 'bg-slate-800 text-blue-400' : '' %>"
                    onclick="toggleMobileMenu()"
                >
                    Contact
                </a>
                
                <!-- Theme Toggle for Mobile -->
                <div class="pt-4 border-t border-slate-800">
                    <button 
                        onclick="toggleTheme(); toggleMobileMenu();" 
                        class="w-full flex items-center justify-between py-3 px-4 rounded-lg hover:bg-slate-800 transition-colors"
                    >
                        <span>Toggle Theme</span>
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path class="dark:hidden" fill-rule="evenodd" d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" clip-rule="evenodd"></path>
                            <path class="hidden dark:block" d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z"></path>
                        </svg>
                    </button>
                </div>
            </nav>
        </div>
    </div>
</header>
