<article class="card group hover:scale-105 transition-all duration-300 fade-in">
    <!-- Blog Post Content -->
    <div class="space-y-4">
        <!-- Title and Featured Badge -->
        <div class="flex items-start justify-between">
            <h2 class="text-xl font-semibold text-white group-hover:text-blue-400 transition-colors">
                <a href="/blog/<%= it.post.slug %>" class="hover:underline">
                    <%= it.post.title %>
                </a>
            </h2>
            <% if (it.post.featured) { %>
                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-600 text-white">
                    <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                    </svg>
                    Featured
                </span>
            <% } %>
        </div>
        
        <!-- Excerpt -->
        <% if (it.post.excerpt) { %>
            <p class="text-slate-400 leading-relaxed">
                <%= it.post.excerpt %>
            </p>
        <% } %>
        
        <!-- Tags -->
        <% if (it.post.tags && it.post.tags.length > 0) { %>
            <div class="flex flex-wrap gap-2">
                <% it.post.tags.forEach(tag => { %>
                    <span class="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-slate-800 text-slate-300 border border-slate-700">
                        #<%= tag %>
                    </span>
                <% }) %>
            </div>
        <% } %>
        
        <!-- Read More Button -->
        <div class="pt-4">
            <a 
                href="/blog/<%= it.post.slug %>" 
                class="btn btn-outline inline-flex items-center space-x-2"
            >
                <span>Read More</span>
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"/>
                </svg>
            </a>
        </div>
        
        <!-- Post Meta -->
        <div class="flex items-center justify-between pt-4 border-t border-slate-800 text-sm text-slate-500">
            <div class="flex items-center space-x-4">
                <span class="flex items-center space-x-1">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                    </svg>
                    <span>
                        <%= new Date(it.post.published_at || it.post.created_at).toLocaleDateString('en-US', { 
                            year: 'numeric', 
                            month: 'short', 
                            day: 'numeric' 
                        }) %>
                    </span>
                </span>
                
                <!-- Reading time estimate -->
                <span class="flex items-center space-x-1">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                    <span>
                        <%= Math.ceil(it.post.content.split(' ').length / 200) %> min read
                    </span>
                </span>
            </div>
            
            <!-- Publication Status -->
            <div class="flex items-center space-x-2">
                <% if (it.post.published) { %>
                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-600 text-white">
                        <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                        </svg>
                        Published
                    </span>
                <% } else { %>
                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-600 text-white">
                        <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"/>
                        </svg>
                        Draft
                    </span>
                <% } %>
                
                <% if (it.showActions) { %>
                    <div class="flex space-x-2 ml-4">
                        <button 
                            onclick="editPost(<%= it.post.id %>)"
                            class="text-blue-400 hover:text-blue-300 transition-colors"
                            title="Edit post"
                        >
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                            </svg>
                        </button>
                        <button 
                            onclick="deletePost(<%= it.post.id %>)"
                            class="text-red-400 hover:text-red-300 transition-colors"
                            title="Delete post"
                        >
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                            </svg>
                        </button>
                    </div>
                <% } %>
            </div>
        </div>
    </div>
</article>
