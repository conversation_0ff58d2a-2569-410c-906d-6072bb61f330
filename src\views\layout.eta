<!DOCTYPE html>
<html lang="en" data-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= it.title %> | Portfolio</title>
    <meta name="description" content="<%= it.description || 'Personal portfolio showcasing projects and blog posts' %>">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=JetBrains+Mono:ital,wght@0,100..800;1,100..800&display=swap" rel="stylesheet">
    
    <!-- Styles -->
    <link rel="stylesheet" href="/css/style.css">
    
    <!-- Meta tags for SEO -->
    <meta property="og:title" content="<%= it.title %> | Portfolio">
    <meta property="og:description" content="<%= it.description || 'Personal portfolio showcasing projects and blog posts' %>">
    <meta property="og:type" content="website">
    <meta property="og:url" content="<%= it.currentUrl || '' %>">
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="<%= it.title %> | Portfolio">
    <meta name="twitter:description" content="<%= it.description || 'Personal portfolio showcasing projects and blog posts' %>">
</head>
<body class="min-h-screen bg-slate-900 text-slate-100 font-mono">
    <!-- Header -->
    <%~ includeFile('partials/header', it) %>
    
    <!-- Main Content -->
    <main class="flex-1">
        <%~ it.body %>
    </main>
    
    <!-- Footer -->
    <%~ includeFile('partials/footer', it) %>
    
    <!-- Scripts -->
    <script src="/js/main.js"></script>
    
    <!-- Theme Toggle Script -->
    <script>
        // Theme toggle functionality
        function toggleTheme() {
            const html = document.documentElement;
            const currentTheme = html.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
            
            html.setAttribute('data-theme', newTheme);
            localStorage.setItem('theme', newTheme);
        }
        
        // Load saved theme
        const savedTheme = localStorage.getItem('theme') || 'dark';
        document.documentElement.setAttribute('data-theme', savedTheme);
        
        // Smooth scroll for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
        
        // Intersection Observer for fade-in animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };
        
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('visible');
                }
            });
        }, observerOptions);
        
        // Observe all fade-in elements
        document.querySelectorAll('.fade-in').forEach(el => {
            observer.observe(el);
        });
        
        // Form submission handling
        document.querySelectorAll('form[data-ajax]').forEach(form => {
            form.addEventListener('submit', async function(e) {
                e.preventDefault();
                
                const formData = new FormData(this);
                const submitBtn = this.querySelector('button[type="submit"]');
                const originalText = submitBtn.textContent;
                
                // Show loading state
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<span class="loading"></span> Sending...';
                
                try {
                    const response = await fetch(this.action, {
                        method: this.method,
                        body: formData
                    });
                    
                    const result = await response.json();
                    
                    if (result.success) {
                        // Show success message
                        showNotification('Message sent successfully!', 'success');
                        this.reset();
                    } else {
                        showNotification(result.message || 'An error occurred', 'error');
                    }
                } catch (error) {
                    showNotification('Network error. Please try again.', 'error');
                } finally {
                    // Reset button
                    submitBtn.disabled = false;
                    submitBtn.textContent = originalText;
                }
            });
        });
        
        // Notification system
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `fixed top-4 right-4 p-4 rounded-lg shadow-lg z-50 transition-all duration-300 transform translate-x-full ${
                type === 'success' ? 'bg-green-600 text-white' :
                type === 'error' ? 'bg-red-600 text-white' :
                'bg-blue-600 text-white'
            }`;
            notification.textContent = message;
            
            document.body.appendChild(notification);
            
            // Animate in
            setTimeout(() => {
                notification.classList.remove('translate-x-full');
            }, 100);
            
            // Remove after 5 seconds
            setTimeout(() => {
                notification.classList.add('translate-x-full');
                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 300);
            }, 5000);
        }
        
        // Mobile menu toggle
        function toggleMobileMenu() {
            const menu = document.getElementById('mobile-menu');
            const isOpen = menu.classList.contains('translate-x-0');
            
            if (isOpen) {
                menu.classList.remove('translate-x-0');
                menu.classList.add('translate-x-full');
            } else {
                menu.classList.remove('translate-x-full');
                menu.classList.add('translate-x-0');
            }
        }
        
        // Close mobile menu when clicking outside
        document.addEventListener('click', function(e) {
            const menu = document.getElementById('mobile-menu');
            const menuButton = document.querySelector('[data-mobile-menu-toggle]');
            
            if (menu && !menu.contains(e.target) && !menuButton.contains(e.target)) {
                menu.classList.remove('translate-x-0');
                menu.classList.add('translate-x-full');
            }
        });
    </script>
</body>
</html>
